## Quick Start

2. **Build and Deploy**

   ```bash
   npm run build
   firebase deploy --only functions
   ```

3. **Deploy Rules**

   ```bash
   firebase deploy --only firestore:rules
   firebase deploy --only storage:rules
   ```

4. Export Indexes:
   ```bash
   firebase firestore:indexes > firestore.indexes.json && firebase deploy --only firestore:indexes
   ```

5. For local debug you should use `npm run debug`
Here is example how to trigger ton transaction monitor function
```
curl -X POST \
   http://127.0.0.1:5001/marketplace-362c0/us-central1/tonTransactionMonitor \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "account_tx",
    "account_id": "0:e2ff794b102b69812ba6163b31962c8f8216a879455ee487ebb1edb7a5c80c8d",
    "lt": **************,
    "tx_hash": "152ecf1fa439ba307de19e71043621ff0e8f18f6aaf18accd932372efd24d437"
}'
```