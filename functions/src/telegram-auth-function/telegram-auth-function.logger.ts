import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const TelegramAuthLogger = {
  logAuthStarted(): void {
    logger.info(
      "Starting Telegram authentication",
      LogOperations.TELEGRAM_AUTH,
      {}
    );
  },

  logValidationResult(data: { isValid: boolean; requestData: any }) {
    logger.info(
      "Telegram data validation result",
      LogOperations.TELEGRAM_AUTH,
      {
        isValid: data.isValid,
        requestData: data.requestData,
      }
    );
  },

  logUserCreated(data: { userId: string; telegramId: string }) {
    logger.info(
      `New Telegram user created: ${data.userId}`,
      LogOperations.TELEGRAM_AUTH,
      {
        userId: data.userId,
        action: "user_created",
      }
    );
  },

  logAuthError(data: { error: unknown; requestData: any }) {
    logger.error(
      "Telegram authentication error",
      data.error,
      LogOperations.TELEGRAM_AUTH,
      {
        requestData: data.requestData,
      }
    );
  },

  logDevelopmentMode() {
    logger.info(
      "Using development mode with mock data",
      LogOperations.TELEGRAM_AUTH,
      { mode: "development" }
    );
  },

  logMockUserParsed(data: {
    telegramId: string;
    username?: string;
    firstName?: string;
  }) {
    logger.info("Mock user data parsed", LogOperations.TELEGRAM_AUTH, data);
  },

  logValidationStarted() {
    logger.info("Validating Telegram data", LogOperations.TELEGRAM_AUTH, {
      mode: "production",
    });
  },

  logFirebaseUserFound(data: { userId: string }) {
    logger.info(
      "Found existing Firebase Auth user",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logFirebaseUserCreating(data: { userId: string }) {
    logger.info(
      "Creating new Firebase Auth user",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logFirebaseUserCreated(data: { userId: string }) {
    logger.info(
      "Firebase Auth user created successfully",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logCustomClaimsSet(data: { userId: string }) {
    logger.info(
      "Custom claims set successfully",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logAppConfigLoaded(data: { config: any }) {
    logger.info("App config loaded", LogOperations.FIREBASE_AUTH, data);
  },

  logCustomTokenCreated(data: { userId: string }) {
    logger.info(
      "Custom token created successfully",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },
  logFirebaseAuthError(data: { error: unknown; userId: string }) {
    logger.error(
      "Firebase Auth user creation error",
      data.error,
      LogOperations.FIREBASE_AUTH,
      { userId: data.userId }
    );
  },
};
