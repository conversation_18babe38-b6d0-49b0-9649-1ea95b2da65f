import * as CryptoJ<PERSON> from "crypto-js";
import * as admin from "firebase-admin";
import { getEnv, isDevelopment } from "../config";
import {
  Role,
  UserEntity,
  formatDateToFirebaseTimestamp,
} from "../marketplace-shared";
import {
  DBUserCollection,
  getCurrentDateFirestoreTimestamp,
} from "../services/db.service";
import {
  throwFirebaseAuthError,
  throwIAMPermissionError,
  throwInvalidTelegramData,
  throwUserDataMissing,
} from "./telegram-auth-function.validator";
import { TelegramAuthLogger } from "./telegram-auth-function.logger";

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface SignInWithTelegramParams {
  initData: string;
  useLocalBotToken?: boolean;
}

export interface SignInWithTelegramResult {
  customToken: string;
  user: UserEntity;
}

export function validateTelegramWebAppData(initData: string, botToken: string) {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get("hash");

    if (!hash) {
      return { isValid: false, error: "Hash is missing" };
    }

    // Remove hash from params for validation
    urlParams.delete("hash");

    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort()) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join("\n");

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, "WebAppData");

    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(
      dataCheckString,
      secretKey
    ).toString();

    // Verify hash
    if (hash !== expectedHash && !isDevelopment()) {
      return { isValid: false, error: "Invalid hash" };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get("auth_date");
    if (!authDate) {
      return { isValid: false, error: "Auth date is missing" };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: "Auth data is too old" };
    }

    // Parse user data
    const userParam = urlParams.get("user");
    if (!userParam) {
      return { isValid: false, error: "User data is missing" };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: `Validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function validateTelegramData(
  initData: string,
  botToken: string,
  isDevMode: boolean
) {
  let telegramUser: TelegramUser;
  let telegramId: string;

  if (isDevMode && initData.includes("mock_hash_for_development")) {
    TelegramAuthLogger.logDevelopmentMode();

    const urlParams = new URLSearchParams(initData);
    const userParam = urlParams.get("user");

    if (!userParam) {
      throwUserDataMissing();
    }

    telegramUser = JSON.parse(userParam);
    telegramId = telegramUser.id.toString();

    TelegramAuthLogger.logMockUserParsed({
      telegramId,
      username: telegramUser.username,
      firstName: telegramUser.first_name,
    });
  } else {
    TelegramAuthLogger.logValidationStarted();
    const validation = validateTelegramWebAppData(initData, botToken);

    TelegramAuthLogger.logValidationResult({
      isValid: validation.isValid,
      requestData: {
        error: validation.error,
        hasUser: !!validation.user,
      },
    });

    if (!validation.isValid || !validation.user) {
      throwInvalidTelegramData(validation.error);
    }

    telegramUser = validation.user;
    telegramId = telegramUser.id.toString();
  }

  return { telegramUser, telegramId };
}

export async function createFirebaseAuthUser(
  userId: string,
  userRecord: UserEntity,
  telegramUser: TelegramUser,
  telegramId: string
) {
  try {
    try {
      await admin.auth().getUser(userId);
      TelegramAuthLogger.logFirebaseUserFound({ userId });
    } catch (getUserError: any) {
      if (getUserError?.errorInfo?.code === "auth/user-not-found") {
        // Create Firebase Auth user if it doesn't exist
        TelegramAuthLogger.logFirebaseUserCreating({ userId });
        await admin.auth().createUser({
          uid: userId,
          displayName: userRecord.displayName ?? undefined,
          photoURL: userRecord.photoURL ?? undefined,
        });
        TelegramAuthLogger.logFirebaseUserCreated({ userId });
      } else {
        throw getUserError;
      }
    }

    // Prepare custom claims for the token
    const customClaims = {
      tg_id: telegramId,
      provider: "telegram",
      telegram_user: {
        id: telegramUser.id,
        first_name: telegramUser.first_name,
        last_name: telegramUser.last_name,
        username: telegramUser.username,
      },
    };

    // Set custom claims on the Firebase Auth user
    await admin.auth().setCustomUserClaims(userId, customClaims);
    TelegramAuthLogger.logCustomClaimsSet({ userId });

    TelegramAuthLogger.logAppConfigLoaded({ config: getEnv() });

    // Create custom token using Firebase Admin SDK
    const customToken = await admin
      .auth()
      .createCustomToken(userId, customClaims);
    TelegramAuthLogger.logCustomTokenCreated({ userId });

    return { customToken };
  } catch (authError: any) {
    TelegramAuthLogger.logFirebaseAuthError({ error: authError, userId });

    // Handle specific IAM permission errors
    if (
      authError?.message?.includes("iam.serviceAccounts.signBlob") ||
      authError?.errorInfo?.code === "auth/insufficient-permission"
    ) {
      throwIAMPermissionError();
    }

    // Re-throw other auth errors
    throwFirebaseAuthError(authError.message);
  }
}

export async function findOrCreateUser(
  telegramUser: TelegramUser,
  telegramId: string
) {
  // Check if user already exists
  const existingUserQuery = await DBUserCollection.where(
    "tg_id",
    "==",
    telegramId
  )
    .limit(1)
    .get();

  let userId: string;
  let userRecord: UserEntity;

  if (!existingUserQuery.empty) {
    // User exists, get their data
    const existingUserDoc = existingUserQuery.docs[0];
    userId = existingUserDoc.id;
    userRecord = existingUserDoc.data();
  } else {
    // Create new user
    const newUserRef = DBUserCollection.doc();
    userId = newUserRef.id;

    userRecord = {
      id: userId,
      email: null,
      displayName:
        telegramUser.first_name +
        (telegramUser.last_name ? ` ${telegramUser.last_name}` : ""),
      photoURL: telegramUser.photo_url ?? null,
      role: Role.USER,
      tg_id: telegramId,
      telegram_handle: telegramUser.username,
      balance: {
        sum: 0,
        locked: 0,
      },
      withdrawal_24h: {
        amount: 0,
        lastResetAt: formatDateToFirebaseTimestamp(
          getCurrentDateFirestoreTimestamp()
        ),
      },
    };

    await newUserRef.set(userRecord);
  }

  return { userId, userRecord };
}
