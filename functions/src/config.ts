export interface AppEnv {
  app: {
    environment: string;
    project_id: string;
  };
  telegram: {
    bot_token: string;
    local_bot_token: string;
    api_id: number;
    api_hash: string;
  };
  ton: {
    rpc_url_testnet?: string;
    rpc_url_mainnet?: string;
    marketplace_wallet: string;
    marketplace_wallet_mnemonic: string;
    network: string;
    api_key?: string;
  };
  firebase?: {
    service_account_key?: string;
  };
  url: {
    web_app_url: string;
    webhook_url: string;
    auth_token: string;
  };
}

let cachedEnv: AppEnv | null = null;

export function getEnv(): AppEnv {
  if (cachedEnv) {
    return cachedEnv;
  }

  cachedEnv = {
    app: {
      environment: process.env.APP_ENVIRONMENT ?? "development",
      project_id: process.env.GCLOUD_PROJECT ?? process.env.GCP_PROJECT ?? "",
    },
    telegram: {
      bot_token: process.env.TELEGRAM_BOT_TOKEN ?? "",
      local_bot_token: process.env.TELEGRAM_LOCAL_BOT_TOKEN ?? "",
      api_id: parseInt(process.env.TELEGRAM_API_ID ?? "0"),
      api_hash: process.env.TELEGRAM_API_HASH ?? "",
    },
    ton: {
      rpc_url_testnet: process.env.TON_RPC_URL_TESTNET,
      rpc_url_mainnet: process.env.TON_RPC_URL_MAINNET,
      marketplace_wallet: process.env.TON_MARKETPLACE_WALLET ?? "",
      marketplace_wallet_mnemonic:
        process.env.TON_MARKETPLACE_WALLET_MNEMONIC ?? "",
      network: process.env.TON_NETWORK ?? "mainnet",
      api_key: process.env.TON_API_KEY,
    },
    firebase: {
      service_account_key: process.env.FIREBASE_SERVICE_ACCOUNT_KEY,
    },
    url: {
      web_app_url: process.env.WEB_APP_URL ?? "",
      webhook_url: process.env.WEBHOOK_URL ?? "",
      auth_token: process.env.AUTH_TOKEN ?? "",
    },
  };

  return cachedEnv;
}

export function getTonRpcUrl() {
  const env = getEnv();
  const network = env.ton.network;

  let rpcUrl: string;

  rpcUrl = env.ton.rpc_url_mainnet as string;

  if (!rpcUrl) {
    throw new Error(`TON RPC URL for ${network} network not configured`);
  }

  // Ensure URL ends with / for proper path concatenation
  return rpcUrl.endsWith("/") ? rpcUrl : `${rpcUrl}/`;
}

export function getMarketplaceWallet() {
  const env = getEnv();
  const wallet = env.ton.marketplace_wallet;

  if (!wallet) {
    throw new Error("TON marketplace wallet not configured");
  }

  return wallet;
}

export function getMarketplaceWalletMnemonic() {
  const env = getEnv();
  const mnemonic = env.ton.marketplace_wallet_mnemonic;

  if (!mnemonic) {
    throw new Error("TON marketplace wallet mnemonic not configured");
  }

  return mnemonic;
}

export const getBotAppUrl = () => {
  const url = getEnv().url.web_app_url;

  if (!url) {
    throw new Error("BOT_APP_URL environment variable is not configured");
  }

  return url;
};

export const getBotAuthToken = () => {
  const token = getEnv().url.auth_token;

  if (!token) {
    throw new Error("AUTH_TOKEN environment variable is not configured");
  }

  return token;
};

export function getTelegramBotToken(useLocalBotToken?: boolean) {
  const env = getEnv();
  const token = useLocalBotToken
    ? env.telegram.local_bot_token
    : env.telegram.bot_token;

  if (!token) {
    throw new Error("Telegram bot token not configured");
  }

  return token;
}

export function getTelegramApiId() {
  const env = getEnv();
  const apiId = env.telegram.api_id;

  if (!apiId || apiId === 0) {
    throw new Error("Telegram API ID not configured");
  }

  return apiId;
}

export function getTelegramApiHash() {
  const env = getEnv();
  const apiHash = env.telegram.api_hash;

  if (!apiHash) {
    throw new Error("Telegram API hash not configured");
  }

  return apiHash;
}

export function isDevelopment() {
  const env = getEnv();
  return env.app.environment !== "production";
}

export const CORS_CONFIG = isDevelopment()
  ? [
      /^https?:\/\/localhost(:\d+)?$/,
      /^https:\/\/.*\.vercel\.app$/,
      /^https:\/\/.*\.ngrok-free\.app$/,
      /^https:\/\/.*\.us-central1\.run\.app$/,
      /^https:\/\/.*\.europe-central2\.run\.app$/,
    ]
  : [getEnv().url.web_app_url, getEnv().url.webhook_url];
