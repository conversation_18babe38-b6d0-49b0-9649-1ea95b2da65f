import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const BotHealthCheckLogger = {
  logHealthCheckStarted({ status }: { status: string }) {
    logger.info("Bot health check started", {
      operation: LogOperations.BOT_HEALTH_CHECK,
      service: "bot-health-check-function",
      status,
    });
  },

  logHealthCheckCall({
    healthCheckUrl,
    hasAuthentication,
  }: {
    healthCheckUrl: string;
    hasAuthentication: boolean;
  }) {
    logger.info("Making health check call", {
      operation: LogOperations.BOT_HEALTH_CHECK,
      service: "bot-health-check-function",
      healthCheckUrl,
      hasAuthentication,
    });
  },

  logHealthCheckResponse({ responseData }: { responseData: any }) {
    logger.info("Health check response received", {
      operation: LogOperations.BOT_HEALTH_CHECK,
      service: "bot-health-check-function",
      responseData,
    });
  },

  logHealthCheckPassed({ status }: { status: string }) {
    logger.info("Bot health check passed", {
      operation: LogOperations.BOT_HEALTH_CHECK,
      service: "bot-health-check-function",
      status,
    });
  },

  logHealthCheckUnhealthy({
    status,
    responseData,
  }: {
    status: string;
    responseData: any;
  }) {
    logger.info("Bot health check unhealthy", {
      operation: LogOperations.BOT_HEALTH_CHECK,
      service: "bot-health-check-function",
      status,
      responseData,
    });
  },

  logHealthCheckFailed({ error, status }: { error: unknown; status: string }) {
    logger.error("Bot health check failed", {
      operation: LogOperations.BOT_HEALTH_CHECK,
      service: "bot-health-check-function",
      error,
      status,
    });
  },

  logMonitorTriggered({
    status,
    timestamp,
  }: {
    status: string;
    timestamp: string;
  }) {
    logger.info("Bot health monitor triggered", {
      operation: LogOperations.MONITOR,
      service: "bot-health-check-function",
      status,
      timestamp,
    });
  },

  logMonitorCompleted() {
    logger.info("Bot health monitor completed", {
      operation: LogOperations.MONITOR,
      service: "bot-health-check-function",
    });
  },

  logMonitorFailed({ error, status }: { error: unknown; status: string }) {
    logger.error("Bot health monitor failed", {
      operation: LogOperations.MONITOR,
      service: "bot-health-check-function",
      error,
      status,
    });
  },
};
