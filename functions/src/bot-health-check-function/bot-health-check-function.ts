import { onSchedule } from "firebase-functions/v2/scheduler";
import { commonFunctionsConfig } from "../constants";
import { performBotHealthCheck } from "./bot-health-check-function.service";
import { BotHealthCheckLogger } from "./bot-health-check-function.logger";

export const botHealthCheck = onSchedule(
  {
    schedule: "*/15 * * * *", // Every 15 minutes
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      BotHealthCheckLogger.logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await performBotHealthCheck();
      BotHealthCheckLogger.logMonitorCompleted();
    } catch (error) {
      BotHealthCheckLogger.logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
