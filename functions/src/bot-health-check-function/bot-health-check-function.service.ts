import fetch from "node-fetch";
import { getBotAppUrl, getBotAuthToken } from "../config";
import { BOT_HEALTH_CHECK_ENDPOINT } from "../constants";
import { BotHealthCheckLogger } from "./bot-health-check-function.logger";

export async function performBotHealthCheck() {
  try {
    BotHealthCheckLogger.logHealthCheckStarted({ status: "started" });

    const botAppUrl = getBotAppUrl();

    const healthCheckUrl = `${botAppUrl}${BOT_HEALTH_CHECK_ENDPOINT}`;
    BotHealthCheckLogger.logHealthCheckCall({
      healthCheckUrl,
      hasAuthentication: true,
    });

    const authToken = getBotAuthToken();

    const response = await fetch(healthCheckUrl, {
      method: "GET",
      timeout: 30000, // 30 second timeout
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "marketplace-functions/health-check",
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Bot health check failed with status ${response.status}: ${response.statusText}`
      );
    }

    const responseData = await response.json();
    BotHealthCheckLogger.logHealthCheckResponse({ responseData });

    if (responseData.status === "healthy") {
      BotHealthCheckLogger.logHealthCheckPassed({ status: "healthy" });
    } else {
      BotHealthCheckLogger.logHealthCheckUnhealthy({
        status: "unhealthy",
        responseData,
      });
    }

    return {
      success: true,
      status: responseData.status,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    BotHealthCheckLogger.logHealthCheckFailed({
      error,
      status: "failed",
    });
    throw error;
  }
}
