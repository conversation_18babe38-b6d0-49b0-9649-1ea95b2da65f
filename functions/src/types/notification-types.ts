/* eslint-disable no-unused-vars */
/**
 * Notification types enum for unified notification system
 * Used across both marketplace-bot and marketplace-functions repositories
 */
export enum NotificationType {
  SELLER_ORDER_PAID = "SELLER_ORDER_PAID",
  BUYER_GIFT_SENT = "BUYER_GIFT_SENT",
  SELLER_NEW_PROPOSAL = "SELLER_NEW_PROPOSAL",
  PROPOSER_ACCEPTED = "PROPOSER_ACCEPTED",
}

/**
 * Base notification payload interface
 */
export interface BaseNotificationPayload {
  notification_type: NotificationType;
  orderId: string;
  orderNumber?: number;
}

/**
 * Seller order paid notification payload
 */
export interface SellerOrderPaidPayload extends BaseNotificationPayload {
  notification_type: NotificationType.SELLER_ORDER_PAID;
  sellerId: string;
  price?: number;
}

/**
 * Buyer gift sent notification payload
 */
export interface BuyerGiftSentPayload extends BaseNotificationPayload {
  notification_type: NotificationType.BUYER_GIFT_SENT;
  buyerId: string;
}

/**
 * Seller new proposal notification payload
 */
export interface SellerNewProposalPayload extends BaseNotificationPayload {
  notification_type: NotificationType.SELLER_NEW_PROPOSAL;
  sellerId: string;
  proposedPrice: number;
  originalPrice: number;
}

/**
 * Proposer accepted notification payload
 */
export interface ProposerAcceptedPayload extends BaseNotificationPayload {
  notification_type: NotificationType.PROPOSER_ACCEPTED;
  proposerId: string;
  proposedPrice: number;
}

/**
 * Union type for all notification payloads
 */
export type NotificationPayload =
  | SellerOrderPaidPayload
  | BuyerGiftSentPayload
  | SellerNewProposalPayload
  | ProposerAcceptedPayload;

/**
 * Notification result interface
 */
export interface NotificationResult {
  success: boolean;
  message: string;
}
