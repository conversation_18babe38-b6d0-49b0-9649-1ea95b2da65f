import { HttpsError } from "firebase-functions/v2/https";
import { CollectionEntity, CollectionStatus } from "../../marketplace-shared";
import {
  DBCollectionsCollection,
  getCurrentDateFirestoreTimestamp,
} from "../db.service";

export function throwAdminCollectionInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    message ?? "Server error while processing admin collection operation."
  );
}

export const validateCollectionIsActive = (collection: CollectionEntity) => {
  if (!collection.active) {
    throw new HttpsError("failed-precondition", "Collection is not active.");
  }
};

export async function createLimitedCollection(collectionId: string) {
  const newCollection: CollectionEntity = {
    id: collectionId,
    name: "Limited Gift",
    description: "Limited collection",
    status: CollectionStatus.PREMARKET,
    floorPrice: 0.1,
    active: true,
  };

  await DBCollectionsCollection.doc(collectionId).set(newCollection);
  return newCollection;
}

export async function updateCollectionToMarket(collectionId: string) {
  const collectionRef = DBCollectionsCollection.doc(collectionId);
  const collectionDoc = await collectionRef.get();

  if (!collectionDoc.exists) {
    return false;
  }

  const collection = collectionDoc.data();
  if (collection?.launchedAt) {
    return false;
  }

  await collectionRef.update({
    status: CollectionStatus.MARKET,
    launchedAt: getCurrentDateFirestoreTimestamp(),
  });

  return true;
}

export async function ensureCollectionExists(collectionId: string) {
  const collectionDoc = await DBCollectionsCollection.doc(collectionId).get();

  if (!collectionDoc.exists) {
    return await createLimitedCollection(collectionId);
  }

  return { id: collectionDoc.id, ...collectionDoc.data() } as CollectionEntity;
}
