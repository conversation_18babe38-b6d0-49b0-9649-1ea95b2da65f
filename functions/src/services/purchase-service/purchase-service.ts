import * as admin from "firebase-admin";
import { OrderStatus, TxType, UserType } from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../transaction-description-intl-keys";
import { lockFundsWithHistory } from "../balance-service/balance-service";
import { DBOrdersCollection } from "../db.service";
import { addDeadlineIfMarketCollection as addDeadlineIfCollectionIsLaunched } from "../deadline-service/deadline-service";
import {
  notifySellerOrderPaid,
  sendNotificationSafely,
} from "../notification-service";
import {
  validateBuyerPurchase,
  validateSellerPurchase,
} from "../order-service/order-validation-service";
import {
  processFeesAndTransferToSeller,
  processFeesOnPurchase,
} from "./purchase-fee-processing-service";

export async function processPurchase(params: {
  userId: string;
  orderId: string;
  userType: UserType;
}) {
  const { userId, orderId, userType } = params;

  await refundProposals(orderId);

  const validation =
    userType === UserType.BUYER
      ? await validateBuyerPurchase({ userId, orderId })
      : await validateSellerPurchase({ userId, orderId });

  const { order, lockedAmount, lockPercentage } = validation;

  // Helper function to create default fee result
  const createDefaultFeeResult = () => ({
    totalFee: 0,
    referralFee: 0,
    marketplaceFee: 0,
    netAmountToSeller: 0,
  });

  // Apply purchase fees if this is a buyer purchase
  let feeResult = createDefaultFeeResult();
  if (userType === UserType.BUYER) {
    // Process fees and conditionally transfer to seller if gift exists
    const processingFunction = order.gift_id_list
      ? processFeesAndTransferToSeller
      : processFeesOnPurchase;

    feeResult = await processingFunction(order, userId);
  }

  // Lock funds for the user (skip for buyers when order has gift since funds are already transferred)
  const shouldLockFunds = !(userType === UserType.BUYER && order.gift_id_list);

  if (shouldLockFunds) {
    const txType =
      userType === UserType.BUYER
        ? TxType.BUY_LOCK_COLLATERAL
        : TxType.SELL_LOCK_COLLATERAL;
    await lockFundsWithHistory({
      userId,
      amount: lockedAmount - feeResult.totalFee,
      txType,
      orderId,
      descriptionIntlKey:
        userType === UserType.BUYER
          ? TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_BUYER
          : TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_SELLER,
      descriptionIntlParams: {
        amount: lockedAmount.toString(),
        percentage: (lockPercentage * 100).toString(),
        orderPrice: order.price.toString(),
      },
    });
  }

  // Prepare update data
  // If buyer is purchasing an order that already has gift or giftId,
  // set status directly to GIFT_SENT_TO_RELAYER
  const shouldSkipToPaidStatus =
    userType === UserType.BUYER && order.gift_id_list;

  const updateData: any = {
    status: shouldSkipToPaidStatus
      ? OrderStatus.GIFT_SENT_TO_RELAYER
      : OrderStatus.PAID,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  };

  // Set buyer or seller ID based on user type
  if (userType === UserType.BUYER) {
    updateData.buyerId = userId;
  } else {
    updateData.sellerId = userId;
  }

  // Add deadline if collection is in MARKET status
  await addDeadlineIfCollectionIsLaunched(
    order.collectionId,
    orderId,
    updateData
  );

  // Update order
  await DBOrdersCollection.doc(orderId).update(updateData);

  // Send notification to seller when buyer purchases (order status becomes PAID)
  if (userType === UserType.BUYER && updateData.status === OrderStatus.PAID) {
    sendNotificationSafely(
      notifySellerOrderPaid,
      {
        orderId,
        sellerId: order.sellerId!,
        orderNumber: order.number,
        price: order.price,
      },
      "purchase-flow-service"
    );
  }

  return {
    success: true,
    // Structured data for UI to construct localized messages
    purchaseData: {
      userType,
      shouldLockFunds,
      shouldSkipToPaidStatus,
      lockedAmount: shouldLockFunds ? lockedAmount : 0,
      orderAmount: order.price,
      lockPercentage: lockPercentage * 100,
      feeApplied: feeResult.totalFee,
      referralFee: feeResult.referralFee,
      marketplaceFee: feeResult.marketplaceFee,
      netAmountToSeller: feeResult.netAmountToSeller,
    },
    // Legacy fields for backward compatibility (will be removed after UI migration)
    lockedAmount: shouldLockFunds ? lockedAmount : 0,
    orderAmount: order.price,
    lockPercentage: lockPercentage * 100,
    feeApplied: feeResult.totalFee,
    referralFee: feeResult.referralFee,
    marketplaceFee: feeResult.marketplaceFee,
    netAmountToSeller: feeResult.netAmountToSeller,
  };
}
