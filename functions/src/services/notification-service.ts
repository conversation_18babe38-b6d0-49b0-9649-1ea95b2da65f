/* eslint-disable no-unused-vars */
import fetch from "node-fetch";
import { getEnv } from "../config";
import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";
import {
  NotificationType,
  NotificationPayload,
  NotificationResult,
} from "../types/notification-types";

export interface NotifyBuyParams {
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  price?: number;
}

export interface NotifyGiftSendParams {
  orderId: string;
  buyerId: string;
  orderNumber?: number;
}

interface NotificationConfig {
  logMessage: string;
  successMessage: string;
  errorMessage: string;
}

const NOTIFICATION_TIMEOUT = 10000;
const USER_AGENT = "marketplace-functions/notification";

function validateEnvironment(): { botAppUrl: string; authToken: string } {
  const botAppUrl = getEnv().url.webhook_url;
  if (!botAppUrl) {
    throw new Error("Bot webhook URL not configured");
  }

  const authToken = process.env.AUTH_TOKEN;
  if (!authToken) {
    throw new Error("AUTH_TOKEN not configured for bot notifications");
  }

  return { botAppUrl, authToken };
}

function createRequestHeaders(authToken: string): Record<string, string> {
  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${authToken}`,
    "User-Agent": USER_AGENT,
  };
}

function formatErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : "Unknown error";
}

async function sendUnifiedNotification(
  payload: NotificationPayload,
  config: NotificationConfig
): Promise<NotificationResult> {
  try {
    const { botAppUrl, authToken } = validateEnvironment();

    logger.info(config.logMessage, LogOperations.BOT_OPERATION, payload);

    const notificationUrl = `${botAppUrl}/notify`;
    const headers = createRequestHeaders(authToken);

    const response = await fetch(notificationUrl, {
      method: "POST",
      timeout: NOTIFICATION_TIMEOUT,
      headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Bot notification failed: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const responseData = await response.json();

    logger.info(config.successMessage, LogOperations.BOT_OPERATION, {
      ...payload,
      responseData,
    });

    return {
      success: true,
      message: config.successMessage,
    };
  } catch (error) {
    logger.error(
      config.errorMessage,
      error,
      LogOperations.BOT_OPERATION,
      payload
    );

    return {
      success: false,
      message: `Failed to send notification: ${formatErrorMessage(error)}`,
    };
  }
}

export async function notifySellerOrderPaid(
  params: NotifyBuyParams
): Promise<NotificationResult> {
  const config: NotificationConfig = {
    logMessage: "Sending seller order paid notification",
    successMessage: "Seller notification sent successfully",
    errorMessage: "Failed to send seller notification",
  };

  const payload: NotificationPayload = {
    notification_type: NotificationType.SELLER_ORDER_PAID,
    orderId: params.orderId,
    sellerId: params.sellerId,
    orderNumber: params.orderNumber,
    price: params.price,
  };

  return sendUnifiedNotification(payload, config);
}

export async function notifyBuyerGiftSent(
  params: NotifyGiftSendParams
): Promise<NotificationResult> {
  const config: NotificationConfig = {
    logMessage: "Sending buyer gift sent notification",
    successMessage: "Buyer notification sent successfully",
    errorMessage: "Failed to send buyer notification",
  };

  const payload: NotificationPayload = {
    notification_type: NotificationType.BUYER_GIFT_SENT,
    orderId: params.orderId,
    buyerId: params.buyerId,
    orderNumber: params.orderNumber,
  };

  return sendUnifiedNotification(payload, config);
}

export async function sendNotificationSafely<
  T extends NotifyBuyParams | NotifyGiftSendParams
>(
  notificationFn: (params: T) => Promise<NotificationResult>,
  params: T,
  context: string
) {
  try {
    const result = await notificationFn(params);
    if (!result.success) {
      logger.error(
        `Notification failed in ${context}`,
        new Error(result.message),
        LogOperations.BOT_OPERATION,
        params
      );
    }
  } catch (error) {
    logger.error(
      `Notification error in ${context}`,
      error,
      LogOperations.BOT_OPERATION,
      params
    );
  }
}
