import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export const TransactionHistoryServiceLogger = {
  logTransactionCreated({
    userId,
    txType,
    amount,
  }: {
    userId: string;
    txType: string;
    amount: number;
  }) {
    logger.info(
      "Transaction history record created",
      LogOperations.CREATE_TRANSACTION_RECORD,
      {
        userId,
        txType,
        amount,
      }
    );
  },
};

export function logTransactionHistoryError({
  error,
  operation,
  userId,
  txType,
}: {
  error: unknown;
  operation: string;
  userId?: string;
  txType?: string;
}) {
  logger.error(
    `Error in transaction history service: ${operation}`,
    error,
    LogOperations.TRANSACTION_HISTORY,
    {
      operation,
      userId,
      txType,
    }
  );
}
