import * as admin from "firebase-admin";

import { OrderEntity } from "../../marketplace-shared";
import { DBOrdersCollection } from "../db.service";

export async function getOrderById(orderId: string) {
  const orderDoc = await DBOrdersCollection.doc(orderId).get();
  return orderDoc.exists
    ? ({ id: orderDoc.id, ...orderDoc.data() } as OrderEntity)
    : null;
}

export async function updateOrder(
  orderId: string,
  updates: Partial<OrderEntity>
) {
  await DBOrdersCollection.doc(orderId).update({
    ...updates,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });
}

export function calculateSellerCollateralAdjustment(
  oldPrice: number,
  newPrice: number,
  sellerLockPercentageBPS: number
) {
  const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

  const oldCollateral = oldPrice * sellerLockPercentage;
  const newCollateral = newPrice * sellerLockPercentage;
  const refundAmount = Math.max(0, oldCollateral - newCollateral);

  return {
    oldCollateral,
    newCollateral,
    refundAmount,
  };
}
