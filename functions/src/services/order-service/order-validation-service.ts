import {
  validateBuyerMarketCollectionRestriction,
  validateCollectionAndFloorPrice,
} from "../collection-service/collection.validator";

import { HttpsError } from "firebase-functions/v2/https";
import { ORDER_ERRORS } from "../../error-messages";
import {
  CollectionStatus,
  OrderEntity,
  OrderStatus,
  UserType,
  Role,
} from "../../marketplace-shared";
import {
  DBCollectionsCollection,
  DBOrdersCollection,
  DBUserCollection,
} from "../db.service";
import { validateBalanceAndCalculateLock } from "../financial.validator";
import {
  createOrderCreationPipeline,
  createOrderPurchasePipeline,
  OrderCreationValidationParams,
} from "../validation.composer";

export async function validateOrderCreation(
  params: OrderCreationValidationParams
) {
  const { userId, collectionId, price, userType } = params;

  // Use composable validation pipeline
  const pipeline = createOrderCreationPipeline()
    .add(async (p) => {
      await validateCollectionAndFloorPrice({
        collectionId: p.collectionId,
        amount: p.price,
      });
    })
    .addConditional(
      (p) => p.userType === UserType.BUYER,
      async (p) => {
        const collection = await validateCollectionAndFloorPrice({
          collectionId: p.collectionId,
          amount: p.price,
        });
        validateBuyerMarketCollectionRestriction(collection, p.userType);
      }
    );

  await pipeline.execute(params);

  // Get collection and balance data for return
  const collection = await validateCollectionAndFloorPrice({
    collectionId,
    amount: price,
  });

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: price,
      userType,
    });

  return { collection, lockedAmount, lockPercentage };
}

export async function validateOrderCreationForMarketCollection(params: {
  userId: string;
  collectionId: string;
  price: number;
  userType: UserType;
}) {
  const { userId, collectionId, price, userType } = params;

  const collection = await validateCollectionAndFloorPrice({
    collectionId,
    amount: price,
  });

  // Validate that buyers cannot create orders for MARKET collections
  validateBuyerMarketCollectionRestriction(collection, userType);

  // For sellers in MARKET collections, skip balance validation and return 0 amounts
  if (userType === UserType.SELLER) {
    return { collection, lockedAmount: 0, lockPercentage: 0 };
  }

  // For buyers, still validate balance and calculate lock
  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: price,
      userType,
    });

  return { collection, lockedAmount, lockPercentage };
}

export async function validateBuyerPurchase(params: {
  userId: string;
  orderId: string;
}) {
  const { userId, orderId } = params;

  // Use composable validation pipeline
  const pipeline = createOrderPurchasePipeline().add(async (p) => {
    const order = await validateOrderExists(p.orderId);
    validateOrderAvailableForPurchase(order);
    validateBuyerPurchaseConstraints(order, p.userId);
  });

  await pipeline.execute({ userId, orderId, userType: UserType.BUYER });

  // Get order and balance data for return
  const order = await validateOrderExists(orderId);
  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: order.price,
      userType: UserType.BUYER,
      orderFees: order.fees,
    });

  return { order, lockedAmount, lockPercentage };
}

export async function validateSellerPurchase(params: {
  userId: string;
  orderId: string;
}) {
  const { userId, orderId } = params;

  // Use composable validation pipeline
  const pipeline = createOrderPurchasePipeline().add(async (p) => {
    const order = await validateOrderExists(p.orderId);
    validateOrderAvailableForPurchase(order);
    validateSellerPurchaseConstraints(order, p.userId);
  });

  await pipeline.execute({ userId, orderId, userType: UserType.SELLER });

  // Get order and balance data for return
  const order = await validateOrderExists(orderId);
  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: order.price,
      userType: UserType.SELLER,
      orderFees: order.fees,
    });

  return { order, lockedAmount, lockPercentage };
}

export async function validateOrderExists(
  orderId: string
): Promise<OrderEntity> {
  const orderDoc = await DBOrdersCollection.doc(orderId).get();

  if (!orderDoc.exists) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
        fallbackMessage: "Order not found.",
      })
    );
  }

  return { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;
}

export function validateOrderAvailableForPurchase(order: OrderEntity) {
  if (order.status !== OrderStatus.ACTIVE) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
        fallbackMessage: "Order is not available for purchase.",
      })
    );
  }
}

export function validateBuyerPurchaseConstraints(
  order: OrderEntity,
  buyerId: string
): void {
  if (order.sellerId === buyerId) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.CANNOT_PURCHASE_OWN_ORDER,
        fallbackMessage: "Cannot purchase your own order.",
      })
    );
  }
}

export function validateSellerPurchaseConstraints(
  order: OrderEntity,
  sellerId: string
) {
  if (order.buyerId === sellerId) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.CANNOT_PURCHASE_OWN_ORDER,
        fallbackMessage: "Cannot purchase your own order.",
      })
    );
  }
}

export async function validateSellerCreatedOrdersLimit(
  sellerId: string,
  collectionId: string
) {
  const collectionDoc = await DBCollectionsCollection.doc(collectionId).get();

  if (!collectionDoc.exists) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: ORDER_ERRORS.COLLECTION_NOT_FOUND,
        fallbackMessage: "Collection not found.",
      })
    );
  }

  const collection = collectionDoc.data();
  if (collection?.status !== CollectionStatus.MARKET) {
    return; // Only apply limit to MARKET collections
  }

  const createdOrdersQuery = DBOrdersCollection.where(
    "sellerId",
    "==",
    sellerId
  )
    .where("collectionId", "==", collectionId)
    .where("status", "==", OrderStatus.CREATED);

  const createdOrdersSnapshot = await createdOrdersQuery.get();
  const createdOrdersCount = createdOrdersSnapshot.size;

  if (createdOrdersCount >= 3) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.TOO_MANY_CREATED_ORDERS,
        fallbackMessage:
          "You already have 3 orders that need to be activated. Please activate existing orders before creating new ones.",
      })
    );
  }
}

export async function validateCancellationPermission(
  order: OrderEntity,
  userId: string
) {
  // Check if the user is an admin - admins can cancel any order
  const userDoc = await DBUserCollection.doc(userId).get();
  const userData = userDoc.exists ? userDoc.data() : null;
  const isAdmin = userData?.role === Role.ADMIN;

  // Skip permission validation for admin users
  if (!isAdmin) {
    if (order.buyerId !== userId && order.sellerId !== userId) {
      throw new HttpsError(
        "permission-denied",
        JSON.stringify({
          errorKey: ORDER_ERRORS.ORDER_NOT_OWNED_BY_USER,
          fallbackMessage:
            "You can only cancel orders where you are the buyer or seller.",
        })
      );
    }
  }

  if (order.status === OrderStatus.FULFILLED) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
        fallbackMessage: "Cannot cancel a fulfilled order.",
      })
    );
  }

  if (order.status === OrderStatus.CANCELLED) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
        fallbackMessage: "Order is already cancelled.",
      })
    );
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
        fallbackMessage:
          "Cannot cancel an order where the gift has already been sent to relayer.",
      })
    );
  }

  // Special validation for CREATED orders
  if (order.status === OrderStatus.CREATED) {
    // Only sellers can cancel CREATED orders, and only if they are the seller
    if (order.sellerId !== userId) {
      throw new HttpsError(
        "permission-denied",
        JSON.stringify({
          errorKey: ORDER_ERRORS.ORDER_NOT_OWNED_BY_USER,
          fallbackMessage: "Only the seller can cancel a CREATED order.",
        })
      );
    }
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
        fallbackMessage:
          "Cannot cancel a CREATED order. Please wait for a buyer or let it expire.",
      })
    );
  }
}

export function validateSecondaryMarketAvailability(order: OrderEntity): void {
  if (!order.secondaryMarketPrice || order.secondaryMarketPrice <= 0) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.NOT_AVAILABLE_ON_SECONDARY_MARKET,
        fallbackMessage: "Order is not available on secondary market.",
      })
    );
  }
}

export function validateSecondaryMarketOrderStatus(order: OrderEntity): void {
  if (order.status !== OrderStatus.PAID) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
        fallbackMessage:
          "Only paid orders can be purchased on secondary market.",
      })
    );
  }
}

export function validateOrderParticipants(order: OrderEntity): void {
  if (!order.buyerId || !order.sellerId) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.MISSING_PARTICIPANTS,
        fallbackMessage: "Order is missing buyer or seller information.",
      })
    );
  }
}
