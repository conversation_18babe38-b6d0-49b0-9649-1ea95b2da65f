import { LogOperations } from "../../constants";
import { logger } from "firebase-functions/v2";

export const DeadlineServiceLogger = {
  logDeadlineAdded({
    orderId,
    collectionId,
    deadline,
  }: {
    orderId: string;
    collectionId: string;
    deadline: string;
  }) {
    logger.info(
      "Added dynamic deadline to order for MARKET collection",
      LogOperations.DEADLINE_ADDED,
      {
        orderId,
        collectionId,
        deadline,
      }
    );
  },

  logNoDeadlineUpdatesNeeded({ collectionId }: { collectionId: string }) {
    logger.info(
      "No orders need deadline updates for collection",
      LogOperations.NO_UPDATES_NEEDED,
      {
        collectionId,
      }
    );
  },

  logDeadlineBatchProcessed({
    batchNumber,
    updatedCount,
    collectionId,
  }: {
    batchNumber: number;
    updatedCount: number;
    collectionId: string;
  }) {
    logger.info(
      "Processed batch for deadline updates",
      LogOperations.BATCH_PROCESSED,
      {
        batchNumber,
        updatedCount,
        collectionId,
      }
    );
  },

  logDeadlineUpdatesCompleted({
    totalUpdatedCount,
    collectionId,
  }: {
    totalUpdatedCount: number;
    collectionId: string;
  }) {
    logger.info(
      "Added dynamic deadlines to orders for collection",
      LogOperations.DEADLINES_COMPLETED,
      {
        totalUpdatedCount,
        collectionId,
      }
    );
  },

  logDeadlineServiceError({
    error,
    operation,
    orderId,
    collectionId,
  }: {
    error: unknown;
    operation: LogOperations;
    orderId?: string;
    collectionId?: string;
  }) {
    logger.error(`Error in deadline service: ${operation}`, error, operation, {
      orderId,
      collectionId,
    });
  },
};
