import { HttpsError } from "firebase-functions/v2/https";
import { GIFT_ERRORS } from "../../error-messages";
import { GiftStatus, OrderStatus } from "../../marketplace-shared";
import { DBOrdersCollection } from "../db.service";
import { getGiftById } from "./gift-service";

export async function isGiftUsedInOrder(giftId: string) {
  try {
    const ordersQuery = await DBOrdersCollection.where(
      "gift_id_list",
      "array-contains",
      giftId
    ).get();

    // Check if any order with this giftId has status: paid, active, or gift_sent_to_relayer
    for (const orderDoc of ordersQuery.docs) {
      const orderData = orderDoc.data();
      const status = orderData.status;

      if (
        status === OrderStatus.PAID ||
        status === OrderStatus.ACTIVE ||
        status === OrderStatus.GIFT_SENT_TO_RELAYER
      ) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("Error checking if gift is used in order:", error);
    throw new Error("Failed to validate gift usage");
  }
}

export async function validateGiftForLinking(giftId: string) {
  const gift = await getGiftById(giftId);
  if (!gift) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: GIFT_ERRORS.GIFT_NOT_FOUND,
        fallbackMessage: "Gift not found",
      })
    );
  }

  if (gift.status !== GiftStatus.DEPOSITED) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: GIFT_ERRORS.GIFT_INVALID_STATUS,
        fallbackMessage: "Gift must have status 'deposited' to be linked",
      })
    );
  }

  const isUsed = await isGiftUsedInOrder(giftId);
  if (isUsed) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: GIFT_ERRORS.GIFT_ALREADY_LINKED,
        fallbackMessage: "Gift is already linked to another order",
      })
    );
  }

  return gift;
}
