import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export function logWalletValidation({
  walletAddress,
  isValid,
}: {
  walletAddress: string;
  isValid: boolean;
}) {
  logger.info("TON wallet validation completed", {
    operation: LogOperations.TON_WALLET,
    service: "ton-wallet-service",
    walletAddress,
    isValid,
  });
}

export function logWalletBinding({
  userId,
  walletAddress,
}: {
  userId: string;
  walletAddress: string;
}) {
  logger.info("TON wallet bound to user", {
    operation: LogOperations.TON_WALLET,
    service: "ton-wallet-service",
    userId,
    walletAddress,
  });
}

export function logTonWalletServiceError({
  error,
  operation,
  userId,
  walletAddress,
}: {
  error: unknown;
  operation: string;
  userId?: string;
  walletAddress?: string;
}) {
  logger.error(`Error in TON wallet service: ${operation}`, {
    operation: LogOperations.TON_WALLET,
    service: "ton-wallet-service",
    error,
    operationName: operation,
    userId,
    walletAddress,
  });
}
