import { Api, TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import {
  getTelegramApiHash,
  getTelegramApiId,
  getTelegramBotToken,
} from "../config";

interface LimitedCollection {
  id: string;
  limited: boolean;
  upgradeStars: number | null;
}

export async function fetchLimitedCollections(): Promise<LimitedCollection[]> {
  const botAuthToken = getTelegramBotToken();
  const apiId = getTelegramApiId();
  const apiHash = getTelegramApiHash();

  const stringSession = new StringSession("");
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    await client.start({ botAuthToken });
    
    const result = await client.invoke(new Api.payments.GetStarGifts({ hash: 0 }));

    if (!("gifts" in result)) {
      return [];
    }

    return (result as any).gifts
      .filter((gift: any) => gift.limited === true)
      .map((gift: any) => ({
        id: gift.id.toString(),
        limited: gift.limited,
        upgradeStars: gift.upgradeStars ? gift.upgradeStars.toString() : null,
      }));
  } finally {
    await client.disconnect();
  }
}
