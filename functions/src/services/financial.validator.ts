import { HttpsError } from "firebase-functions/v2/https";
import { ORDER_ERRORS, VALIDATION_ERRORS } from "../error-messages";
import { UserType } from "../marketplace-shared";
import { bpsToDecimal, safeMultiply } from "../utils";
import { getUserBalance } from "./balance-service/balance-service";
import { calculateFeeAmount, getAppConfig } from "./fee-service/fee-service";

export function validatePositiveAmount(
  amount: number,
  fieldName = "amount"
): void {
  if (amount <= 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.POSITIVE_AMOUNT_REQUIRED,
        params: { fieldName },
        fallbackMessage: `${fieldName} must be greater than 0.`,
      })
    );
  }
}

export async function validateSufficientBalance(
  userId: string,
  requiredAmount: number,
  operation?: string
) {
  const userBalance = await getUserBalance(userId);
  const availableBalance = userBalance.sum - userBalance.locked;

  if (availableBalance < requiredAmount) {
    const fallbackMessage = operation
      ? `Insufficient balance to ${operation}. Required: ${requiredAmount} TON, Available: ${availableBalance} TON.`
      : `Insufficient balance. Required: ${requiredAmount} TON, Available: ${availableBalance} TON.`;

    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INSUFFICIENT_BALANCE,
        params: {
          required: requiredAmount.toString(),
          available: availableBalance.toString(),
        },
        fallbackMessage,
      })
    );
  }
}

export async function validateBalanceAndCalculateLock(params: {
  userId: string;
  amount: number;
  userType: UserType;
  orderFees?: any; // Using any to match the existing OrderFees interface
}): Promise<{ lockedAmount: number; lockPercentage: number }> {
  const { userId, amount, userType, orderFees } = params;

  let lockPercentageBPS: number;

  if (orderFees) {
    // Use fees from existing order
    lockPercentageBPS =
      userType === UserType.BUYER
        ? orderFees.buyer_locked_percentage
        : orderFees.seller_locked_percentage;
  } else {
    // Use current app config for new orders
    const config = await getAppConfig();
    lockPercentageBPS =
      userType === UserType.BUYER
        ? config?.buyer_lock_percentage ?? 2100 // 21% default
        : config?.seller_lock_percentage ?? 2100; // 21% default
  }

  const lockPercentage = bpsToDecimal(lockPercentageBPS);
  const lockedAmount = safeMultiply(amount, lockPercentage);

  let totalRequiredAmount = lockedAmount;
  let purchaseFeeAmount = 0;

  if (userType === UserType.BUYER) {
    let purchaseFeeBPS = 0;

    if (orderFees) {
      purchaseFeeBPS = orderFees.purchase_fee ?? 0;
    } else {
      const config = await getAppConfig();
      purchaseFeeBPS = config?.purchase_fee ?? 0;
    }

    if (purchaseFeeBPS > 0) {
      purchaseFeeAmount = calculateFeeAmount(amount, purchaseFeeBPS);
      totalRequiredAmount = lockedAmount + purchaseFeeAmount;
    }
  }

  await validateSufficientBalance(
    userId,
    totalRequiredAmount,
    userType === UserType.BUYER ? "make purchase" : "create order"
  );

  return { lockedAmount, lockPercentage };
}
