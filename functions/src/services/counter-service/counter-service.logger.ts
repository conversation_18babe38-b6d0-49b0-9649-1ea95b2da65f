import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export function logCounterIncremented({
  counterName,
  newValue,
}: {
  counterName: string;
  newValue: number;
}) {
  logger.info("Counter incremented", {
    operation: LogOperations.COUNTER_SERVICE,
    service: "counter-service",
    counterName,
    newValue,
  });
}

export function logCounterServiceError({
  error,
  operation,
  counterName,
}: {
  error: unknown;
  operation: string;
  counterName?: string;
}) {
  logger.error(`Error in counter service: ${operation}`, {
    operation: LogOperations.COUNTER_SERVICE,
    service: "counter-service",
    error,
    counterName,
    operationName: operation,
  });
}
