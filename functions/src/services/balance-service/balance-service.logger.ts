import { LogOperations } from "../../constants";
import { logger } from "firebase-functions/v2";

export const BalanceServiceLogger = {
  logBalanceUpdate({
    userId,
    sumChange,
    lockedChange,
    operation,
  }: {
    userId: string;
    sumChange: number;
    lockedChange: number;
    operation: string;
  }) {
    logger.info(
      `Balance updated for user ${userId}`,
      LogOperations.BALANCE_OPERATION,
      {
        userId,
        sumChange,
        lockedChange,
        operation,
      }
    );
  },

  logInsufficientFunds({
    userId,
    requiredAmount,
    availableBalance,
    operation,
  }: {
    userId: string;
    requiredAmount: number;
    availableBalance: number;
    operation: string;
  }) {
    logger.info(
      `Insufficient funds for user ${userId}`,
      LogOperations.INSUFFICIENT_FUNDS,
      {
        userId,
        requiredAmount,
        availableBalance,
        operation,
      }
    );
  },

  logFundsValidated({
    userId,
    amount,
    operation,
  }: {
    userId: string;
    amount: number;
    operation: string;
  }) {
    logger.info(
      `Funds validated for user ${userId}`,
      LogOperations.FUNDS_VALIDATION,
      {
        userId,
        amount,
        operation,
      }
    );
  },

  logBalanceServiceError({
    error,
    operation,
    userId,
  }: {
    error: unknown;
    operation: string;
    userId?: string;
  }) {
    logger.error(
      `Error in balance service: ${operation}`,
      error,
      LogOperations.BALANCE_OPERATION,
      {
        userId,
      }
    );
  },
};
