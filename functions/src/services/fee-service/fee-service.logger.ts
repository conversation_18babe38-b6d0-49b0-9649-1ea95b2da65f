import { LogOperations } from "../../constants";
import { logger } from "firebase-functions/v2";

export const FeeServiceLogger = {
  logAppConfigNotFound() {
    logger.info(
      "App config not found, using zero fees",
      LogOperations.APP_CONFIG_FETCH,
      {}
    );
  },

  logAdminUserNotFound() {
    logger.info("No admin user found", LogOperations.ADMIN_USER_LOOKUP, {});
  },

  logFeeApplied({
    feeAmount,
    feeType,
    userId,
    netAmount,
  }: {
    feeAmount: number;
    feeType: string;
    userId?: string;
    netAmount?: number;
  }) {
    logger.info(`${feeType} fee applied`, LogOperations.FEE_APPLIED, {
      feeAmount,
      feeType,
      userId,
      netAmount,
    });
  },

  logReferralFeeApplied({
    feeAmount,
    feeType,
    referrerFeeRate,
    referrerId,
    referralId,
  }: {
    feeAmount: number;
    feeType: string;
    referrerFeeRate: number;
    referrerId: string;
    referralId: string;
  }) {
    logger.info("Purchase referral fee applied", LogOperations.REFERRAL_FEE, {
      feeAmount,
      feeType,
      referrerFeeRate,
      referrerId,
      referralId,
    });
  },

  logCustomReferralFee({
    referrerFeeRate,
    referrerId,
  }: {
    referrerFeeRate: number;
    referrerId: string;
  }) {
    logger.info("Using custom referral fee", LogOperations.CUSTOM_REFERRAL, {
      referrerFeeRate,
      referrerId,
    });
  },

  logOrderReferralFee({
    referrerFeeRate,
    referrerId,
  }: {
    referrerFeeRate: number;
    referrerId: string;
  }) {
    logger.info("Using order referral fee", LogOperations.ORDER_REFERRAL, {
      referrerFeeRate,
      referrerId,
    });
  },

  logReferrerNotFound({ referralId }: { referralId: string }) {
    logger.info(
      "Referrer with tg_id not found, adding full fee to marketplace",
      LogOperations.REFERRER_NOT_FOUND,
      { referralId }
    );
  },

  logTotalFeeApplied({
    feeAmount,
    feeType,
    referralFee,
    marketplaceFee,
  }: {
    feeAmount: number;
    feeType: string;
    referralFee: number;
    marketplaceFee: number;
  }) {
    logger.info(`${feeType} fee applied`, LogOperations.TOTAL_FEE, {
      feeAmount,
      feeType,
      referralFee,
      marketplaceFee,
    });
  },

  logFeeServiceError({
    error,
    operation,
    userId,
    amount,
  }: {
    error: unknown;
    operation: LogOperations;
    userId?: string;
    amount?: number;
  }) {
    logger.error(`Error in fee service: ${operation}`, error, operation, {
      userId,
      amount,
    });
  },
};
