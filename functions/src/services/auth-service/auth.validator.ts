import { HttpsError, CallableRequest } from "firebase-functions/v2/https";
import { AUTH_ERRORS } from "../../error-messages";
import { UserEntity, Role } from "../../marketplace-shared";
import { getUserById } from "./auth.service";

type AuthenticatedRequest = CallableRequest & {
  auth: NonNullable<CallableRequest["auth"]>;
};

export function requireAuthentication(
  request: CallableRequest
): AuthenticatedRequest {
  if (!request.auth) {
    throw new HttpsError(
      "unauthenticated",
      JSON.stringify({
        errorKey: AUTH_ERRORS.UNAUTHENTICATED,
        fallbackMessage: "Authentication required.",
      })
    );
  }
  return request as AuthenticatedRequest;
}

export function validateUserOwnership(
  authRequest: AuthenticatedRequest,
  userId: string,
  operation?: string
): void {
  if (authRequest.auth.uid !== userId) {
    const fallbackMessage = operation
      ? `Permission denied for ${operation}.`
      : "Permission denied.";

    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: AUTH_ERRORS.PERMISSION_DENIED_WITH_OPERATION,
        params: { operation: operation || "this operation" },
        fallbackMessage,
      })
    );
  }
}

export function validateSellerOwnership(
  authRequest: AuthenticatedRequest,
  sellerId: string
) {
  validateUserOwnership(authRequest, sellerId, "seller operations");
}

export function validateBuyerOwnership(
  authRequest: AuthenticatedRequest,
  buyerId: string
) {
  validateUserOwnership(authRequest, buyerId, "buyer operations");
}

export function requireAdminAccess(user: UserEntity) {
  if (user.role !== Role.ADMIN) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: AUTH_ERRORS.ADMIN_ONLY,
        fallbackMessage: "Admin access required.",
      })
    );
  }
}

export function requireTelegramId(user: UserEntity, operation?: string) {
  if (!user.tg_id) {
    const fallbackMessage = operation
      ? `User must have a Telegram ID configured to ${operation}.`
      : "User must have a Telegram ID configured to perform this operation.";

    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TELEGRAM_ID_REQUIRED,
        fallbackMessage,
      })
    );
  }
}

export function requireTonWallet(user: UserEntity, operation?: string): void {
  if (!user.ton_wallet_address) {
    const fallbackMessage = operation
      ? `User must have a TON wallet configured to ${operation}.`
      : "User must have a TON wallet configured to perform this operation.";

    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TON_WALLET_REQUIRED,
        fallbackMessage,
      })
    );
  }
}

export async function validateUserExists(userId: string) {
  const user = await getUserById(userId);
  if (!user) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: AUTH_ERRORS.USER_NOT_FOUND,
        fallbackMessage: "User not found.",
      })
    );
  }
  return user;
}

