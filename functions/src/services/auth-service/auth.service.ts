import { CallableRequest, HttpsError } from "firebase-functions/v2/https";
import { UserEntity } from "../../marketplace-shared";
import {
  requireAuthentication,
  requireAdminAccess,
  validateUserExists,
} from "./auth.validator";
import { DBUserCollection } from "../db.service";

export interface ValidatedUserRequest extends AuthenticatedRequest {
  user: UserEntity;
}

// Re-export commonly used functions for backward compatibility
export { requireAuthentication } from "./auth.validator";

// Simplified user data function using validator
export async function getUserData(userId: string) {
  return await validateUserExists(userId);
}

// Simplified admin role function using validator
export async function requireAdminRole(userId: string) {
  const user = await getUserData(userId);
  requireAdminAccess(user);
  return user;
}

// Utility function for authentication and user data retrieval
export async function authenticateAndGetUser(request: CallableRequest) {
  const authRequest = requireAuthentication(request);
  const user = await getUserData(authRequest.auth.uid);
  return { request: authRequest, user };
}

type AuthenticatedRequest = CallableRequest & {
  auth: NonNullable<CallableRequest["auth"]>;
};

export const requireAuth: (
  request: CallableRequest
) => asserts request is AuthenticatedRequest = (
  request: CallableRequest
): asserts request is AuthenticatedRequest => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }
};

export interface UserLookupParams {
  userId?: string;
  tgId?: string;
}

export interface UserLookupResult {
  success: boolean;
  userId?: string;
  message?: string;
}

export async function getUserById(userId: string) {
  const userDoc = await DBUserCollection.doc(userId).get();

  if (!userDoc.exists) {
    return null;
  }

  return { id: userDoc.id, ...userDoc.data() } as UserEntity;
}
