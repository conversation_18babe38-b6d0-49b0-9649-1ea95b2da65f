import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const UtilsLogger = {
  logDivisionByZeroError({
    amount,
    divisor,
  }: {
    amount: number;
    divisor: number;
  }) {
    logger.error("Division by zero error in safeDivide function", {
      operation: LogOperations.SAFE_DIVIDE,
      service: "utils",
      error: new Error("Division by zero"),
      amount,
      divisor,
    });
  },
};
