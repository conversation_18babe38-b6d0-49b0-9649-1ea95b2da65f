import { REFERRAL_TIERS } from "../marketplace-shared";

export function calculateReferralPoints(referralCount: number) {
  let totalPoints = 0;

  for (const tier of REFERRAL_TIERS) {
    if (referralCount >= tier.friends) {
      totalPoints += tier.points;
    } else {
      break;
    }
  }

  return totalPoints;
}

export function getUserPoints(referralCount: number) {
  const calculatedPoints = calculateReferralPoints(referralCount);

  return calculatedPoints;
}
