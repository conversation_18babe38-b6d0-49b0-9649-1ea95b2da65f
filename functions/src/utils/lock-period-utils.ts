import {
  CollectionEntity,
  AppConfigEntity,
} from "../marketplace-shared";

export const DEFAULT_LOCK_PERIOD_DAYS = 21;

export function getEffectiveLockPeriod(
  collection: CollectionEntity | null,
  appConfig: AppConfigEntity | null
): number {
  if (collection?.lock_period && collection.lock_period > 0) {
    return collection.lock_period;
  }
  if (appConfig?.lock_period && appConfig.lock_period > 0) {
    return appConfig.lock_period;
  }
  return DEFAULT_LOCK_PERIOD_DAYS;
}
