import { onSchedule } from "firebase-functions/v2/scheduler";
import { logger } from "firebase-functions/v2";
import { commonFunctionsConfig } from "../constants";
import { checkLimitedCollections } from "./limited-collections-monitor.service";

export const limitedCollectionsMonitor = onSchedule(
  {
    schedule: "0 */6 * * *",
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      await checkLimitedCollections();
      logger.info("Limited collections monitor completed");
    } catch (error) {
      logger.error("Limited collections monitor failed", error);
      throw error;
    }
  }
);
