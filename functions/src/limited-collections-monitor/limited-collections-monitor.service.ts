import { CollectionStatus } from "../marketplace-shared";
import { addDeadlineToOrders } from "../services/deadline-service/deadline-service";
import {
  ensureCollectionExists,
  updateCollectionToMarket,
} from "../services/collection-service/collection-service";
import { fetchLimitedCollections } from "../services/telegram-api.service";

interface LimitedCollection {
  id: string;
  limited: boolean;
  upgradeStars: number | null;
}

async function processUpgradeableCollection(collection: LimitedCollection) {
  const existingCollection = await ensureCollectionExists(collection.id);

  if (existingCollection.status !== CollectionStatus.PREMARKET) {
    return;
  }

  const updated = await updateCollectionToMarket(collection.id);
  if (updated) {
    await addDeadlineToOrders(collection.id);
  }
}

export async function checkLimitedCollections() {
  const limitedGifts = await fetchLimitedCollections();

  if (limitedGifts.length === 0) {
    return;
  }

  await Promise.all(
    limitedGifts.map((gift) => ensureCollectionExists(gift.id))
  );

  const upgradeableGifts = limitedGifts.filter(
    (gift) => gift.upgradeStars !== null
  );

  if (upgradeableGifts.length === 0) {
    return;
  }

  await Promise.all(upgradeableGifts.map(processUpgradeableCollection));
}
