import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const OrderFunctionsLogger = {
  // Buyer Order Logger Functions
  logBuyerCreateOrderError({
    error,
    requestData,
    userId,
  }: {
    error: unknown;
    requestData?: any;
    userId?: string;
  }) {
    logger.error("Error creating order as buyer", {
      operation: LogOperations.CREATE_ORDER_AS_BUYER,
      service: "buyer-order",
      error,
      requestData,
      userId,
    });
  },

  logBuyerPurchaseError({
    error,
    buyerId,
    orderId,
  }: {
    error: unknown;
    buyerId?: string;
    orderId?: string;
  }) {
    logger.error("Error making purchase as buyer", {
      operation: LogOperations.BUYER_PURCHASE,
      service: "buyer-order",
      error,
      buyerId,
      orderId,
    });
  },

  // Seller Order Logger Functions
  logSellerCreateOrderError({
    error,
    requestData,
    sellerId,
  }: {
    error: unknown;
    requestData?: any;
    sellerId?: string;
  }) {
    logger.error("Error creating order as seller", {
      operation: LogOperations.CREATE_ORDER_AS_SELLER,
      service: "seller-order",
      error,
      sellerId,
      collectionId: requestData?.collectionId,
      price: requestData?.price,
    });
  },

  logSellerPurchaseError({
    error,
    requestData,
    userId,
  }: {
    error: unknown;
    requestData?: any;
    userId?: string;
  }) {
    logger.error("Error making purchase as seller", {
      operation: LogOperations.PURCHASE_AS_SELLER,
      service: "seller-order",
      error,
      requestData,
      userId,
    });
  },

  // General Order Logger Functions
  logCancelOrderRequest({
    orderId,
    userId,
    environment,
  }: {
    orderId: string;
    userId: string;
    environment?: string;
  }) {
    logger.debug("Cancel order request received", {
      operation: LogOperations.CANCEL_ORDER,
      service: "general-order",
      orderId,
      userId,
      environment,
    });
  },

  logCancelOrderError({
    error,
    orderId,
    userId,
  }: {
    error: unknown;
    orderId?: string;
    userId?: string;
  }) {
    logger.error("Error cancelling order", {
      operation: LogOperations.CANCEL_USER_ORDER,
      service: "general-order",
      error,
      orderId,
      userId,
    });
  },

  // Fulfill and Resell Logger Functions
  logOrderFulfilledAndResellCreated({
    originalOrderId,
    newOrderId,
    userId,
    price,
  }: {
    originalOrderId: string;
    newOrderId: string;
    userId: string;
    price: number;
  }) {
    logger.info("Order fulfilled and resell order created", {
      operation: LogOperations.FULFILL_AND_RESELL,
      service: "fulfill-and-resell",
      originalOrderId,
      newOrderId,
      userId,
      price,
    });
  },

  logFulfillAndResellError({
    error,
    requestData,
    userId,
  }: {
    error: unknown;
    requestData?: any;
    userId?: string;
  }) {
    logger.error("Error fulfilling order and creating resell order", {
      operation: LogOperations.FULFILL_AND_RESELL,
      service: "fulfill-and-resell",
      error,
      requestData,
      userId,
    });
  },

  // Transaction History Logger Functions
  logTransactionRecordCreated({
    userId,
    txType,
    originalAmount,
    signedAmount,
    orderId,
  }: {
    userId: string;
    txType: string;
    originalAmount: number;
    signedAmount: number;
    orderId?: string;
  }) {
    logger.info("Transaction history record created", {
      operation: LogOperations.CREATE_TRANSACTION_RECORD,
      service: "transaction-history",
      userId,
      txType,
      originalAmount,
      signedAmount,
      orderId,
    });
  },

  // Fee Service Logger Functions
  logAppConfigNotFound() {
    logger.warn("App config not found, using zero fees", {
      operation: LogOperations.APP_CONFIG_FETCH,
      service: "fee-service",
    });
  },
};
