import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const WithdrawalLimitLogger = {
  logLimitCheckCompleted({
    userId,
    requestedAmount,
    maxWithdrawalAmount,
    currentWithdrawn,
    remainingLimit,
    canWithdraw,
  }: {
    userId: string;
    requestedAmount: number;
    maxWithdrawalAmount: number;
    currentWithdrawn: number;
    remainingLimit: number;
    canWithdraw: boolean;
  }) {
    logger.info("Withdrawal limit check completed", {
      operation: LogOperations.CHECK_WITHDRAWAL_LIMIT,
      service: "withdrawal-limit",
      userId,
      requestedAmount,
      maxWithdrawalAmount,
      currentWithdrawn,
      remainingLimit,
      canWithdraw,
    });
  },

  logLimitCheckError({
    error,
    userId,
    requestedAmount,
    maxWithdrawalAmount,
  }: {
    error: unknown;
    userId: string;
    requestedAmount: number;
    maxWithdrawalAmount: number;
  }) {
    logger.error("Error checking withdrawal limit", {
      operation: LogOperations.CHECK_WITHDRAWAL_LIMIT,
      service: "withdrawal-limit",
      error,
      userId,
      requestedAmount,
      maxWithdrawalAmount,
    });
  },

  logTrackingUpdated({
    userId,
    withdrawnAmount,
    newWithdrawnAmount,
    resetTime,
  }: {
    userId: string;
    withdrawnAmount: number;
    newWithdrawnAmount: number;
    resetTime: Date;
  }) {
    logger.info("Withdrawal tracking updated", {
      operation: LogOperations.UPDATE_WITHDRAWAL_TRACKING,
      service: "withdrawal-limit",
      userId,
      withdrawnAmount,
      newWithdrawnAmount,
      resetTime,
    });
  },

  logTrackingUpdateError({
    error,
    userId,
    withdrawnAmount,
  }: {
    error: unknown;
    userId: string;
    withdrawnAmount: number;
  }) {
    logger.error("Error updating withdrawal tracking", {
      operation: LogOperations.UPDATE_WITHDRAWAL_TRACKING,
      service: "withdrawal-limit",
      error,
      userId,
      withdrawnAmount,
    });
  },
};
