import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../constants";
import { PROPOSAL_ERRORS, VALIDATION_ERRORS } from "../error-messages";
import {
  getUserData,
  requireAuthentication,
} from "../services/auth-service/auth.service";
import { requireTelegramId } from "../services/auth-service/auth.validator";
import {
  acceptProposal as acceptProposalService,
  cancelProposal as cancelProposalService,
  proposeOrderPrice as proposeOrderPriceService,
} from "./proposal-service";

export const acceptProposal = onCall<{
  orderId: string;
  proposalId: string;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { orderId, proposalId } = request.data;
  const sellerId = authRequest.auth.uid;

  if (!orderId || !proposalId) {
    throw new HttpsError(
      "invalid-argument",
      "Order ID and proposal ID are required"
    );
  }

  try {
    // Validate user has telegram ID for proposal operations
    const user = await getUserData(sellerId);
    requireTelegramId(user, "accept price proposals");

    const result = await acceptProposalService(orderId, proposalId, sellerId);

    return result;
  } catch (error) {
    console.error("Error accepting proposal:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError("internal", PROPOSAL_ERRORS.FAILED_TO_ACCEPT);
  }
});

export const cancelProposal = onCall<{
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { orderId } = request.data;
  const proposerId = authRequest.auth.uid;

  if (!orderId) {
    throw new HttpsError(
      "invalid-argument",
      VALIDATION_ERRORS.INVALID_ORDER_ID
    );
  }

  try {
    // Validate user has telegram ID for proposal operations
    const user = await getUserData(proposerId);
    requireTelegramId(user, "cancel price proposals");

    const result = await cancelProposalService(orderId, proposerId);

    return result;
  } catch (error) {
    console.error("Error cancelling proposal:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError("internal", PROPOSAL_ERRORS.FAILED_TO_CANCEL);
  }
});

export const proposeOrderPrice = onCall<{
  orderId: string;
  proposedPrice: number;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { orderId, proposedPrice } = request.data;
  const proposerId = authRequest.auth.uid;

  if (!orderId || !proposedPrice || proposedPrice <= 0) {
    throw new HttpsError("invalid-argument", PROPOSAL_ERRORS.INVALID_ARGUMENTS);
  }

  try {
    // Validate user has telegram ID for proposal operations
    const user = await getUserData(proposerId);
    requireTelegramId(user, "create price proposals");

    const result = await proposeOrderPriceService(
      orderId,
      proposerId,
      proposedPrice
    );

    return result;
  } catch (error) {
    console.error("Error proposing order price:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError("internal", PROPOSAL_ERRORS.FAILED_TO_PROPOSE);
  }
});
