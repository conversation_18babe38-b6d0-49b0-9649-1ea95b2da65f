import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const ProposalLogger = {
  logProposalCreated({
    orderId,
    proposerId,
    proposedPrice,
    originalPrice,
    collateralAmount,
  }: {
    orderId: string;
    proposerId: string;
    proposedPrice: number;
    originalPrice: number;
    collateralAmount: number;
  }) {
    logger.info("Price proposal created successfully", {
      operation: LogOperations.PROPOSAL_CREATE,
      service: "proposal-function",
      orderId,
      proposerId,
      proposedPrice,
      originalPrice,
      collateralAmount,
    });
  },

  logProposalCancelled({
    orderId,
    proposerId,
    proposalId,
    refundAmount,
  }: {
    orderId: string;
    proposerId: string;
    proposalId: string;
    refundAmount: number;
  }) {
    logger.info("Price proposal cancelled successfully", {
      operation: LogOperations.PROPOSAL_CANCEL,
      service: "proposal-function",
      orderId,
      proposerId,
      proposalId,
      refundAmount,
    });
  },

  logProposalAccepted({
    orderId,
    proposalId,
    sellerId,
    proposerId,
    proposedPrice,
    originalPrice,
    newOrderPrice,
  }: {
    orderId: string;
    proposalId: string;
    sellerId: string;
    proposerId: string;
    proposedPrice: number;
    originalPrice: number;
    newOrderPrice: number;
  }) {
    logger.info("Price proposal accepted successfully", {
      operation: LogOperations.PROPOSAL_ACCEPT,
      service: "proposal-function",
      orderId,
      proposalId,
      sellerId,
      proposerId,
      proposedPrice,
      originalPrice,
      newOrderPrice,
    });
  },

  logProposalValidationFailed({
    orderId,
    proposerId,
    proposedPrice,
    reason,
  }: {
    orderId: string;
    proposerId: string;
    proposedPrice: number;
    reason: string;
  }) {
    logger.warn("Proposal validation failed", {
      operation: LogOperations.PROPOSAL_VALIDATION,
      service: "proposal-function",
      orderId,
      proposerId,
      proposedPrice,
      reason,
    });
  },

  logProposalError({
    error,
    operation,
    orderId,
    proposerId,
    proposalId,
  }: {
    error: unknown;
    operation: string;
    orderId?: string;
    proposerId?: string;
    proposalId?: string;
  }) {
    logger.error(
      `Error in proposal operation: ${operation}`,
      error,
      LogOperations.PROPOSAL_OPERATION,
      {
        orderId,
        proposerId,
        proposalId,
      }
    );
  },

  logCollateralRefund({
    orderId,
    proposerId,
    refundAmount,
    reason,
  }: {
    orderId: string;
    proposerId: string;
    refundAmount: number;
    reason: string;
  }) {
    logger.info(
      "Proposal collateral refunded",
      LogOperations.PROPOSAL_COLLATERAL_REFUND,
      {
        orderId,
        proposerId,
        refundAmount,
        reason,
      }
    );
  },

  logSellerCollateralAdjustment({
    orderId,
    sellerId,
    oldCollateral,
    newCollateral,
    refundAmount,
  }: {
    orderId: string;
    sellerId: string;
    oldCollateral: number;
    newCollateral: number;
    refundAmount: number;
  }) {
    logger.info(
      "Seller collateral adjusted after proposal acceptance",
      LogOperations.PROPOSAL_SELLER_COLLATERAL_ADJUSTMENT,
      {
        orderId,
        sellerId,
        oldCollateral,
        newCollateral,
        refundAmount,
      }
    );
  },
};
