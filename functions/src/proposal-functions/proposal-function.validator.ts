import { HttpsError } from "firebase-functions/v2/https";
import { PROPOSAL_ERRORS } from "../error-messages";

export function throwOrderNotFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.ORDER_NOT_FOUND,
      fallbackMessage: "Order not found",
    })
  );
}

export function throwProposalOnlyOnSellOrders(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.PROPOSAL_ONLY_ON_SELL_ORDERS,
      fallbackMessage: "Proposals can only be made on sell orders",
    })
  );
}

export function throwNoActiveProposalFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.NO_ACTIVE_PROPOSAL_FOUND,
      fallbackMessage: "No active proposal found for this user",
    })
  );
}

export function throwProposalNotFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.PROPOSAL_NOT_FOUND,
      fallbackMessage: "Proposal not found or not active",
    })
  );
}

export function throwOnlySellerCanAcceptProposal(): never {
  throw new HttpsError(
    "permission-denied",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.ONLY_SELLER_CAN_ACCEPT_PROPOSAL,
      fallbackMessage: "Only the seller can accept proposals",
    })
  );
}

export function throwProposalInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.INTERNAL_ERROR,
      fallbackMessage: message ?? "Server error in proposal operation",
    })
  );
}
