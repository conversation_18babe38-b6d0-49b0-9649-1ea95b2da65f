export const TRANSACTION_DESCRIPTION_INTL_KEYS = {
  // Collateral locking
  LOCKED_COLLATERAL_FOR_BUYER:
    "transaction.description.lockedCollateralForBuyer",
  LOCKED_COLLATERAL_FOR_SELLER:
    "transaction.description.lockedCollateralForSeller",

  // Collateral unlocking
  COLLATER<PERSON>_UNLOCKED_DUE_TO_CANCELLATION:
    "transaction.description.collateralUnlockedDueToCancellation",
  COLLATERAL_UNLOCKED_DUE_TO_ADMIN_CANCELLATION:
    "transaction.description.collateralUnlockedDueToAdminCancellation",
  COLLATERAL_UNLOCKED_DUE_TO_SELLER_CANCELLATION:
    "transaction.description.collateralUnlockedDueToSellerCancellation",
  COLLATERAL_UNLOCKED_DUE_TO_BUYER_CANCELLATION:
    "transaction.description.collateralUnlockedDueToBuyerCancellation",
  UNLOCKED_BUYER_COLLATERAL_FOR_CANCELLED_ORDER:
    "transaction.description.unlockedBuyerCollateralForCancelledOrder",

  // Cancellation penalties and compensation
  CANCELLATION_PENALTY_FOR_SELLER:
    "transaction.description.cancellationPenaltyForSeller",
  CANCELLATION_PENALTY_FOR_BUYER:
    "transaction.description.cancellationPenaltyForBuyer",
  CANCELLATION_COMPENSATION_FROM_BUYER_COLLATERAL:
    "transaction.description.cancellationCompensationFromBuyerCollateral",
  FIXED_CANCELLATION_FEE_PENALTY:
    "transaction.description.fixedCancellationFeePenalty",

  // Fees and earnings
  REFERRAL_FEE_FROM_PURCHASE: "transaction.description.referralFeeFromPurchase",
  RESELL_FEE_EARNINGS_FROM_BUYER_CANCELLATION:
    "transaction.description.resellFeeEarningsFromBuyerCancellation",

  // Wallet operations
  WITHDRAWAL_TO_TON_WALLET: "transaction.description.withdrawalToTonWallet",
  DEPOSIT_FROM_TON_WALLET: "transaction.description.depositFromTonWallet",

  // Order completion
  SALE_COMPLETED_FOR_ORDER: "transaction.description.saleCompletedForOrder",

  // Proposal operations
  PROPOSAL_COLLATERAL_LOCK: "transaction.description.proposalCollateralLock",
  PROPOSAL_COLLATERAL_REFUND:
    "transaction.description.proposalCollateralRefund",
  PROPOSAL_CANCELLATION_FEE: "transaction.description.proposalCancellationFee",

  // Fallback
  UNKNOWN: "transaction.description.unknown",
} as const;
