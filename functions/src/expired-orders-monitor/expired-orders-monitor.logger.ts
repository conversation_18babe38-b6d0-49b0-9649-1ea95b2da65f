import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const ExpiredOrdersMonitorLogger = {
  logProcessingStarted() {
    logger.info(
      "Starting expired orders processing",
      LogOperations.PROCESS_EXPIRED
    );
  },

  logNoOrdersFound() {
    logger.info("No expired orders found", LogOperations.PROCESS_EXPIRED, {});
  },

  logOrdersFound({ count }: { count: number }) {
    logger.info(
      `Found ${count} expired orders to process`,
      LogOperations.PROCESS_EXPIRED,
      { count }
    );
  },

  logOrderSkipped({ orderId, reason }: { orderId: string; reason: string }) {
    logger.info(
      `Skipped order ${orderId}: ${reason}`,
      LogOperations.PROCESS_EXPIRED,
      { orderId, reason }
    );
  },

  logOrderProcessed({
    orderId,
    status,
    message,
  }: {
    orderId: string;
    status: string;
    message: string;
  }) {
    logger.info(
      `Processed order ${orderId}: ${message}`,
      LogOperations.PROCESS_EXPIRED,
      { orderId, status, message }
    );
  },

  logOrderProcessError({
    error,
    orderId,
  }: {
    error: unknown;
    orderId: string;
  }) {
    logger.error(
      `Error processing order ${orderId}`,
      error,
      LogOperations.PROCESS_EXPIRED,
      { orderId }
    );
  },

  logProcessingCompleted(): void {
    logger.info(
      "Expired orders processing completed",
      LogOperations.PROCESS_EXPIRED
    );
  },

  logProcessingError({ error }: { error: unknown }) {
    logger.error(
      "Error in expired orders processing",
      error,
      LogOperations.PROCESS_EXPIRED
    );
  },

  logMonitorTriggered({
    status,
    timestamp,
  }: {
    status: string;
    timestamp: string;
  }) {
    logger.info("Expired orders monitor triggered", LogOperations.MONITOR, {
      status,
      timestamp,
    });
  },

  logMonitorCompleted() {
    logger.info("Expired orders monitor completed", LogOperations.MONITOR, {});
  },

  logMonitorFailed({ error, status }: { error: unknown; status: string }) {
    logger.error(
      "Expired orders monitor failed",
      error,
      LogOperations.MONITOR,
      { status }
    );
  },
};
