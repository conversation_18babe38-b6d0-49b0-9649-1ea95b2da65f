import { HttpsError } from "firebase-functions/v2/https";
import { WITHDRAWAL_ERRORS } from "../error-messages";

export interface WithdrawalLimitInfo {
  canWithdraw: boolean;
  remainingLimit: number;
  resetAt: Date;
}

export function throwAmountBelowMinimum(minAmount: number): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: WITHDRAWAL_ERRORS.AMOUNT_BELOW_MINIMUM,
      params: { minAmount },
      fallbackMessage: `Withdrawal amount must be at least ${minAmount} TON.`,
    })
  );
}

export function throwAmountExceeds24hLimit(
  amount: number,
  withdrawalLimitInfo: WithdrawalLimitInfo
): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: WITHDRAWAL_ERRORS.AMOUNT_EXCEEDS_24H_LIMIT,
      params: {
        requestedAmount: amount,
        remainingLimit: withdrawalLimitInfo.remainingLimit,
        resetAt: withdrawalLimitInfo.resetAt.toISOString(),
      },
      fallbackMessage: `Withdrawal amount exceeds 24-hour limit. You can withdraw up to ${
        withdrawalLimitInfo.remainingLimit
      } TON. Limit resets at ${withdrawalLimitInfo.resetAt.toLocaleString()}.`,
    })
  );
}

export function throwInsufficientBalance(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: WITHDRAWAL_ERRORS.INSUFFICIENT_AVAILABLE_BALANCE,
      fallbackMessage: "Insufficient available balance for withdrawal.",
    })
  );
}

export function throwAmountTooSmallAfterFees(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: WITHDRAWAL_ERRORS.AMOUNT_TOO_SMALL_AFTER_FEES,
      fallbackMessage: "Amount too small after fees.",
    })
  );
}

export function throwWithdrawInternalError(error: any): never {
  throw new HttpsError(
    "internal",
    error.message ?? "Server error while processing withdrawal."
  );
}
