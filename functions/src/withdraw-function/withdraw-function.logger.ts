import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

// Create service logger using functional composition

export function logWithdrawalProcessed(data: {
  userId: string;
  netAmountToUser: number;
  feeAmount: number;
  walletAddress: string;
}) {
  logger.info(
    `Withdrawal processed: ${data.netAmountToUser} TON sent to ${data.walletAddress} (${data.feeAmount} TON fee applied)`,
    LogOperations.WITHDRAW_FUNCTION,
    {
      userId: data.userId,
      netAmountToUser: data.netAmountToUser,
      feeAmount: data.feeAmount,
      walletAddress: data.walletAddress,
    }
  );
}

export function logWithdrawalError(data: {
  error: unknown;
  userId?: string;
  requestData: any;
}) {
  logger.error(
    "Error processing withdrawal",
    data.error,
    LogOperations.WITHDRAW_FUNCTION,
    {
      userId: data.userId,
      requestData: data.requestData,
    }
  );
}
