import { DBUserCollection } from "../services/db.service";
import { MIN_REVENUE_BALANCE_ON_WALLET } from "../constants";
import { MARKETPLACE_REVENUE_USER_ID, UserEntity } from "../marketplace-shared";
import {
  hasAvailableBalance,
  updateUserBalance,
} from "../services/balance-service/balance-service";
import { sendRevenueTransfer } from "../services/ton-wallet-service/ton-wallet-service";
import { safeSubtract } from "../utils";
import {
  throwExceedsMaxWithdrawal,
  throwInsufficientBalance,
  throwInsufficientRevenueBalance,
  throwRevenueAccountNotFound,
} from "./revenue-function.validator";

export interface WithdrawRevenueResult {
  success: boolean;
  message: string;
  totalAmount: number;
  transactionHash?: string;
}

export async function getRevenueUser(): Promise<UserEntity> {
  const revenueDoc = await DBUserCollection.doc(
    MARKETPLACE_REVENUE_USER_ID
  ).get();

  if (!revenueDoc.exists) {
    throwRevenueAccountNotFound();
  }

  return {
    id: revenueDoc.id,
    ...revenueDoc.data(),
  } as UserEntity;
}

export function validateRevenueBalance(
  revenueUser: UserEntity,
  withdrawAmount: number
): void {
  const availableRevenue = revenueUser.balance
    ? safeSubtract(revenueUser.balance.sum, revenueUser.balance.locked)
    : 0;

  if (availableRevenue < MIN_REVENUE_BALANCE_ON_WALLET) {
    throwInsufficientRevenueBalance(MIN_REVENUE_BALANCE_ON_WALLET);
  }

  const maxWithdrawable = safeSubtract(
    availableRevenue,
    MIN_REVENUE_BALANCE_ON_WALLET
  );

  if (withdrawAmount > maxWithdrawable) {
    throwExceedsMaxWithdrawal(maxWithdrawable, MIN_REVENUE_BALANCE_ON_WALLET);
  }
}

export async function validateSufficientBalance(withdrawAmount: number) {
  const hasBalance = await hasAvailableBalance(
    MARKETPLACE_REVENUE_USER_ID,
    withdrawAmount
  );
  if (!hasBalance) {
    throwInsufficientBalance();
  }
}

export async function processRevenueWithdrawal(
  withdrawAmount: number,
  johnDowWallet: string
) {
  await updateUserBalance({
    userId: MARKETPLACE_REVENUE_USER_ID,
    sumChange: -withdrawAmount,
    lockedChange: 0,
  });

  return sendRevenueTransfer(withdrawAmount, johnDowWallet);
}
