import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export function logRevenueWithdrawal({
  withdrawAmount,
  johnDowWallet,
  userId,
  transactionHash,
}: {
  withdrawAmount: number;
  johnDowWallet: string;
  userId: string;
  transactionHash?: string;
}) {
  logger.info(
    `Revenue transferred: ${withdrawAmount.toFixed(
      4
    )} TON sent to John <PERSON> wallet`,
    LogOperations.REVENUE_WITHDRAWAL,
    {
      withdrawAmount,
      johnDowWallet,
      userId,
      transactionHash,
    }
  );
}

export function logRevenueWithdrawalError({
  error,
  withdrawAmount,
  johnDowWallet,
  userId,
}: {
  error: unknown;
  withdrawAmount: number;
  johnDowWallet: string;
  userId?: string;
}) {
  logger.info(
    "Error in withdrawRevenue function",
    error,
    LogOperations.REVENUE_WITHDRAWAL,
    {
      withdrawAmount,
      johnDowWallet,
      userId,
    }
  );
}
