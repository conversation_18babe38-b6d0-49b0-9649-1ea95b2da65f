import { HttpsError } from "firebase-functions/v2/https";

export function throwUnauthenticated(): never {
  throw new HttpsError(
    "unauthenticated",
    "User must be authenticated to withdraw revenue."
  );
}

export function throwInvalidWithdrawAmount(): never {
  throw new HttpsError(
    "invalid-argument",
    "Withdrawal amount must be greater than 0."
  );
}

export function throwInvalidWalletAddress() {
  throw new HttpsError(
    "invalid-argument",
    "John Dow wallet address is required."
  );
}

export function throwRevenueAccountNotFound(): never {
  throw new HttpsError("not-found", "Marketplace revenue account not found.");
}

export function throwInsufficientRevenueBalance(minBalance: number): never {
  throw new HttpsError(
    "failed-precondition",
    `Cannot withdraw when revenue balance is less than ${minBalance} TON.`
  );
}

export function throwExceedsMaxWithdrawal(
  maxAmount: number,
  minBalance: number
): never {
  throw new HttpsError(
    "failed-precondition",
    `Cannot withdraw more than ${maxAmount.toFixed(
      4
    )} TON. Must keep ${minBalance} TON minimum.`
  );
}

export function throwInsufficientBalance(): never {
  throw new HttpsError(
    "failed-precondition",
    "Insufficient revenue balance for withdrawal."
  );
}

export function throwRevenueInternalError(): never {
  throw new HttpsError(
    "internal",
    "An error occurred while processing the revenue withdrawal."
  );
}

// Validation functions moved from revenue-function.service.ts
export interface WithdrawRevenueParams {
  withdrawAmount: number;
  johnDowWallet: string;
}

export function validateWithdrawRevenueParams(
  params: WithdrawRevenueParams
): void {
  const { withdrawAmount, johnDowWallet } = params;

  if (!withdrawAmount || withdrawAmount <= 0) {
    throwInvalidWithdrawAmount();
  }

  if (!johnDowWallet) {
    throwInvalidWalletAddress();
  }
}
