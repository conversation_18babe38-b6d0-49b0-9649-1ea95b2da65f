import { LogOperations } from "../../constants";
import { logger } from "firebase-functions/v2";

export const SecondaryMarketLogger = {
  logSetSecondaryPriceStarted({
    orderId,
    userId,
    secondaryMarketPrice,
  }: {
    orderId: string;
    userId: string;
    secondaryMarketPrice: number;
  }) {
    logger.info(
      "Set secondary market price started",
      LogOperations.SECONDARY_MARKET_OPERATION,
      {
        orderId,
        userId,
        secondaryMarketPrice,
      }
    );
  },

  logSetSecondaryPriceSuccess({
    orderId,
    userId,
    secondaryMarketPrice,
  }: {
    orderId: string;
    userId: string;
    secondaryMarketPrice: number;
  }) {
    logger.info(
      "Secondary market price set successfully",
      LogOperations.SET_SECONDARY_PRICE,
      {
        orderId,
        userId,
        secondaryMarketPrice,
      }
    );
  },

  logSecondaryPurchaseStarted({
    orderId,
    newBuyerId,
    oldBuyerId,
    secondaryMarketPrice,
  }: {
    orderId: string;
    newBuyerId: string;
    oldBuyerId: string;
    secondaryMarketPrice: number;
  }) {
    logger.info(
      "Secondary market purchase started",
      LogOperations.SECONDARY_PURCHASE,
      {
        orderId,
        newBuyerId,
        oldBuyerId,
        secondaryMarketPrice,
      }
    );
  },

  logSecondaryPurchaseSuccess({
    orderId,
    newBuyerId,
    oldBuyerId,
    secondaryMarketPrice,
    feeAmount,
    lockedAmount,
  }: {
    orderId: string;
    newBuyerId: string;
    oldBuyerId: string;
    secondaryMarketPrice: number;
    feeAmount: number;
    lockedAmount: number;
  }) {
    logger.info(
      "Secondary market purchase completed successfully",
      LogOperations.SECONDARY_PURCHASE,
      {
        orderId,
        newBuyerId,
        oldBuyerId,
        secondaryMarketPrice,
        feeAmount,
        lockedAmount,
      }
    );
  },

  logSecondaryMarketError({
    error,
    operation,
    orderId,
    userId,
  }: {
    error: unknown;
    operation: string;
    orderId?: string;
    userId?: string;
  }) {
    logger.error(
      `Error in secondary market operation: ${operation}`,
      error,
      LogOperations.SECONDARY_MARKET_OPERATION,
      {
        orderId,
        userId,
      }
    );
  },
};
