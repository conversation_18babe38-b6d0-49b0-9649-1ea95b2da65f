import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import {
  getUserData,
  requireAuth,
} from "../../services/auth-service/auth.service";
import { requireTelegramId } from "../../services/auth-service/auth.validator";
import { SecondaryMarketLogger } from "./secondary-market-function.logger";
import {
  makeSecondaryMarketPurchase as makeSecondaryPurchaseService,
  setSecondaryMarketPrice as setSecondaryPriceService,
} from "./secondary-market-function.service";
import {
  validateOrderId,
  validateSetSecondaryPriceRequestData,
} from "./secondary-market-function.validator";

export const setSecondaryMarketPrice = onCall<{
  orderId: string;
  secondaryMarketPrice: number;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, secondaryMarketPrice } = request.data;

  requireAuth(request);

  const userId = request.auth?.uid;

  validateSetSecondaryPriceRequestData({ secondaryMarketPrice });

  try {
    // Validate user has telegram ID for secondary market operations
    const user = await getUserData(userId);
    requireTelegramId(user, "set secondary market price");

    SecondaryMarketLogger.logSetSecondaryPriceStarted({
      orderId,
      userId,
      secondaryMarketPrice,
    });

    const result = await setSecondaryPriceService({
      orderId,
      secondaryMarketPrice,
      userId,
    });

    SecondaryMarketLogger.logSetSecondaryPriceSuccess({
      orderId,
      userId,
      secondaryMarketPrice,
    });

    return result;
  } catch (error) {
    SecondaryMarketLogger.logSecondaryMarketError({
      error,
      operation: "set_secondary_price",
      orderId,
      userId,
    });
    throw error;
  }
});

export const makeSecondaryMarketPurchase = onCall<{
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId } = request.data;

  requireAuth(request);

  const newBuyerId = request.auth?.uid;

  validateOrderId(orderId);

  try {
    // Validate user has telegram ID for secondary market operations
    const user = await getUserData(newBuyerId);
    requireTelegramId(user, "make secondary market purchases");

    SecondaryMarketLogger.logSecondaryPurchaseStarted({
      orderId,
      newBuyerId,
      oldBuyerId: "unknown", // Will be determined in service
      secondaryMarketPrice: 0, // Will be determined in service
    });

    const result = await makeSecondaryPurchaseService({
      orderId,
      newBuyerId,
    });

    SecondaryMarketLogger.logSecondaryPurchaseSuccess({
      orderId: result.orderId,
      newBuyerId: result.newBuyerId,
      oldBuyerId: result.oldBuyerId,
      secondaryMarketPrice: result.secondaryMarketPrice,
      feeAmount: result.feeAmount,
      lockedAmount: result.lockedAmount,
    });

    return result;
  } catch (error) {
    SecondaryMarketLogger.logSecondaryMarketError({
      error,
      operation: "secondary_purchase",
      orderId,
      userId: newBuyerId,
    });
    throw error;
  }
});
