import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export const GeneralOrderLogger = {
  logCancelOrderStarted({
    orderId,
    userId,
  }: {
    orderId: string;
    userId: string;
  }) {
    logger.info("Order cancellation started", {
      operation: LogOperations.CANCEL_ORDER,
      service: "general-order-function",
      orderId,
      userId,
    });
  },

  logCancelOrderSuccess({
    orderId,
    userId,
    feeApplied,
    feeType,
  }: {
    orderId: string;
    userId: string;
    feeApplied: number;
    feeType: string;
  }) {
    logger.info("Order cancelled successfully", {
      operation: LogOperations.CANCEL_ORDER,
      service: "general-order-function",
      orderId,
      userId,
      feeApplied,
      feeType,
    });
  },

  logCancelOrderError({
    error,
    orderId,
    userId,
  }: {
    error: unknown;
    orderId?: string;
    userId?: string;
  }) {
    logger.error("Error cancelling order", {
      operation: LogOperations.CANCEL_ORDER,
      service: "general-order-function",
      error,
      orderId,
      userId,
    });
  },
};
