import { HttpsError } from "firebase-functions/v2/https";

export function throwInvalidArguments(): never {
  throw new HttpsError("invalid-argument", "orderId and userId are required.");
}

export function throwPermissionDenied(): never {
  throw new HttpsError(
    "permission-denied",
    "You can only cancel your own orders."
  );
}

export function throwOrderNotFound(): never {
  throw new HttpsError("not-found", "Order not found.");
}

export function throwCancellationFailed(message: string): never {
  throw new HttpsError("failed-precondition", message);
}

export function throwGeneralOrderInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    message ?? "Server error while cancelling order."
  );
}
