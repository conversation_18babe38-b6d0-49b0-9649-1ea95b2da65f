import { processOrderCancellation } from "../../services/order-service/order-cancellation-service";
import {
  validateCancellationPermission,
  validateOrderExists,
} from "../../services/order-service/order-validation-service";
import { throwCancellationFailed } from "./general-order.validator";

export async function cancelOrder({
  orderId,
  userId,
}: {
  orderId: string;
  userId: string;
}) {
  const order = await validateOrderExists(orderId);

  try {
    await validateCancellationPermission(order, userId);

    const result = await processOrderCancellation(order, userId);

    return {
      success: result.success,
      message: result.message,
      order: {
        id: order.id!,
        number: order.number,
        status: "cancelled",
      },
      feeApplied: result?.feeApplied ?? 0,
      feeType: result.feeType,
    };
  } catch (error) {
    if (error instanceof Error) {
      throwCancellationFailed(error.message);
    }
    throw error;
  }
}
