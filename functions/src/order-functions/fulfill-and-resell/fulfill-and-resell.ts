import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import {
  requireAuthentication,
  getUserData,
} from "../../services/auth-service/auth.service";
import { fulfillAndResellOrder } from "./fulfill-and-resell.service";
import {
  throwInvalidParameters,
  throwFulfillAndResellInternalError,
} from "./fulfill-and-resell.validator";
import { requireTelegramId } from "../../services/auth-service/auth.validator";
import { FulfillAndResellLogger } from "./fulfill-and-resell.logger";

export const fulfillOrderAndCreateResellOrder = onCall<{
  orderId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { orderId, price } = request.data;
  const userId = authRequest.auth.uid;

  if (!orderId || !price || price <= 0) {
    throwInvalidParameters();
  }

  try {
    // Validate user has telegram ID for fulfill and resell operations
    const user = await getUserData(userId);
    requireTelegramId(user, "fulfill and resell orders");

    FulfillAndResellLogger.logFulfillAndResellStarted({
      orderId,
      userId,
      price,
    });

    const result = await fulfillAndResellOrder({ orderId, price, userId });

    FulfillAndResellLogger.logFulfillAndResellSuccess({
      originalOrderId: result.originalOrderId,
      newOrderId: result.newOrderId,
      userId,
      price: result.price,
      lockAmount: result.lockAmount,
    });

    return result;
  } catch (error) {
    FulfillAndResellLogger.logFulfillAndResellError({
      error,
      orderId,
      userId,
      price,
    });

    // Re-throw HttpsError as-is
    if (error instanceof HttpsError) {
      throw error;
    }

    throwFulfillAndResellInternalError((error as any).message);
  }
});
