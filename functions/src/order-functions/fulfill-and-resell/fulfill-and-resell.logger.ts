import { LogOperations } from "../../constants";
import { logger } from "firebase-functions/v2";

export const FulfillAndResellLogger = {
  logFulfillAndResellStarted({
    orderId,
    userId,
    price,
  }: {
    orderId: string;
    userId: string;
    price: number;
  }) {
    logger.info(
      "Fulfill and resell order started",
      LogOperations.FULFILL_AND_RESELL,
      {
        orderId,
        userId,
        price,
      }
    );
  },

  logFulfillAndResellSuccess({
    originalOrderId,
    newOrderId,
    userId,
    price,
    lockAmount,
  }: {
    originalOrderId: string;
    newOrderId: string;
    userId: string;
    price: number;
    lockAmount: number;
  }) {
    logger.info(
      "Order fulfilled and resell order created successfully",
      LogOperations.FULFILL_AND_RESELL,
      {
        originalOrderId,
        newOrderId,
        userId,
        price,
        lockAmount,
      }
    );
  },

  logFulfillAndResellError({
    error,
    orderId,
    userId,
    price,
  }: {
    error: unknown;
    orderId?: string;
    userId?: string;
    price?: number;
  }) {
    logger.error(
      "Error fulfilling order and creating resell order",
      error,
      LogOperations.FULFILL_AND_RESELL,
      {
        orderId,
        userId,
        price,
      }
    );
  },
};
