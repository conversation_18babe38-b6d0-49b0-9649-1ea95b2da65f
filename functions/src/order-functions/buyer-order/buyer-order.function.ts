import { onCall } from "firebase-functions/v2/https";
import { UserType } from "../../marketplace-shared";
import { commonFunctionsConfig, LogOperations } from "../../constants";
import { createOrder } from "../../services/order-service/order-creation-service";
import { BuyerOrderLogger } from "./buyer-order.logger";
import { processBuyerPurchase } from "./buyer-order.service";
import {
  validateCreateOrderAsBuyerRequest,
  validateMakePurchaseAsBuyerRequest,
} from "./buyer-order.validator";

export const createOrderAsBuyer = onCall<{
  buyerId: string;
  collectionId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, collectionId, price } = request.data;

  try {
    await validateCreateOrderAsBuyerRequest(request, {
      buyerId,
      collectionId,
      price,
    });

    const result = await createOrder({
      userId: buyerId,
      collectionId,
      price,
      gift_id_list: null,
      userType: UserType.BUYER,
      secondaryMarketPrice: null,
    });

    return result;
  } catch (error) {
    BuyerOrderLogger.logCreateOrderError({
      error,
      operation: LogOperations.CREATE_ORDER_AS_BUYER,
      requestData: request.data,
      userId: request.auth?.uid,
    });
    throw error;
  }
});

export const makePurchaseAsBuyer = onCall<{
  buyerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, orderId } = request.data;

  try {
    await validateMakePurchaseAsBuyerRequest(request, {
      buyerId,
      orderId,
    });

    const result = await processBuyerPurchase({
      buyerId,
      orderId,
    });

    return result;
  } catch (error) {
    BuyerOrderLogger.logPurchaseError({
      error,
      buyerId,
      orderId,
      operation: LogOperations.BUYER_PURCHASE,
    });

    throw error;
  }
});
