import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig, LogOperations } from "../../constants";
import { SellerOrderLogger } from "./seller-order.logger";
import {
  createSellerOrder,
  processSellerPurchase,
  validateCreateOrderRequest,
  validatePurchaseRequest,
} from "./seller-order.service";
import { SellerOrderFunctionErrorHandler } from "./seller-order.validator";

export const createOrderAsSeller = onCall<{
  sellerId: string;
  collectionId: string;
  price: number;
  gift_id_list?: string[];
}>(commonFunctionsConfig, async (request) => {
  const { sellerId, collectionId, price, gift_id_list } = request.data;

  try {
    await validateCreateOrderRequest(request, {
      sellerId,
      collectionId,
      price,
      gift_id_list,
    });

    const result = await createSellerOrder({
      sellerId,
      collectionId,
      price,
      gift_id_list,
    });

    return result;
  } catch (error) {
    SellerOrderLogger.logCreateOrderError({
      error,
      sellerId,
      operation: LogOperations.CREATE_ORDER_AS_SELLER,
      requestData: { collectionId, price, gift_id_list },
    });

    SellerOrderFunctionErrorHandler.throwCreateOrderError(
      (error as any).message
    );
  }
});

export const makePurchaseAsSeller = onCall<{
  sellerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { sellerId, orderId } = request.data;

  try {
    await validatePurchaseRequest(request, {
      sellerId,
      orderId,
    });

    const result = await processSellerPurchase({
      sellerId,
      orderId,
    });

    return result;
  } catch (error) {
    SellerOrderLogger.logPurchaseError({
      error,
      operation: LogOperations.PURCHASE_AS_SELLER,
      requestData: request.data,
      userId: request.auth?.uid,
    });

    SellerOrderFunctionErrorHandler.throwPurchaseError((error as any).message);
  }
});
