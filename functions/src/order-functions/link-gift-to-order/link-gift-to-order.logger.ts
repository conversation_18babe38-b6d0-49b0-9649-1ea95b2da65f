import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export const LinkGiftToOrderLogger = {
  logLinkGiftToOrderFunctionCalled(context: {
    giftId?: string;
    orderId?: string;
    userId?: string;
  }) {
    logger.info("Link gift to order function called", {
      operation: LogOperations.LINK_GIFT_TO_ORDER_START,
      giftId: context.giftId,
      orderId: context.orderId,
      userId: context.userId,
    });
  },

  logGiftValidation(context: {
    giftId: string;
    giftOwnerId: string;
    giftCollectionId: string;
  }) {
    logger.info("Gift validation completed", {
      operation: LogOperations.GIFT_VALIDATION,
      giftId: context.giftId,
      giftOwnerId: context.giftOwnerId,
      giftCollectionId: context.giftCollectionId,
    });
  },

  logOrderValidation(context: {
    orderId: string;
    orderSellerId: string;
    orderStatus: string;
    orderCollectionId: string;
    userId: string;
  }) {
    logger.info("Order validation completed", {
      operation: LogOperations.ORDER_VALIDATION,
      orderId: context.orderId,
      orderSellerId: context.orderSellerId,
      orderStatus: context.orderStatus,
      orderCollectionId: context.orderCollectionId,
      userId: context.userId,
    });
  },

  logGiftLinkedToOrder(context: {
    gift_id_list: string[];
    orderId: string;
    userId: string;
    newOrderStatus: string;
  }) {
    logger.info("Gift successfully linked to order", {
      operation: LogOperations.GIFT_LINKED_TO_ORDER,
      gift_id_list: context.gift_id_list,
      orderId: context.orderId,
      userId: context.userId,
      newOrderStatus: context.newOrderStatus,
    });
  },

  logLinkGiftToOrderCompleted(context: {
    giftId: string;
    orderId: string;
    userId: string;
    success: boolean;
  }) {
    logger.info("Link gift to order function completed", {
      operation: LogOperations.LINK_GIFT_TO_ORDER_COMPLETE,
      giftId: context.giftId,
      orderId: context.orderId,
      userId: context.userId,
      success: context.success,
    });
  },

  logLinkGiftToOrderError(error: unknown) {
    logger.error("Error in link gift to order function", {
      operation: LogOperations.LINK_GIFT_TO_ORDER_ERROR,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
  },
};
