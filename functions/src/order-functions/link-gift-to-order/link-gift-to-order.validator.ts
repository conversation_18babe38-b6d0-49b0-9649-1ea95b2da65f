import { CallableRequest, HttpsError } from "firebase-functions/v2/https";
import { GIFT_ERRORS, ORDER_ERRORS } from "../../error-messages";
import { LinkGiftToOrderLogger } from "./link-gift-to-order.logger";
import {
  OrderEntity,
  OrderStatus,
  GiftEntity,
  UserEntity,
} from "../../marketplace-shared";
import * as admin from "firebase-admin";

// Validation functions
export function validateOrderExists(
  orderDoc: admin.firestore.DocumentSnapshot
): OrderEntity {
  if (!orderDoc.exists) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
        fallbackMessage: "Order not found",
      })
    );
  }
  return { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;
}

export function validateGiftOwnership(
  user: UserEntity | null,
  gift: GiftEntity
) {
  if (!user || user.tg_id !== gift.owner_tg_id) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: GIFT_ERRORS.GIFT_NOT_OWNED_BY_USER,
        fallbackMessage: "Gift does not belong to the current user",
      })
    );
  }
}

export function validateOrderHasSeller(order: OrderEntity) {
  if (!order.sellerId) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
        fallbackMessage: "Order has no seller ID",
      })
    );
  }
}

export function validateUserIsOrderSeller(order: OrderEntity, userId: string) {
  if (order.sellerId !== userId) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
        fallbackMessage: "User is not the seller of this order",
      })
    );
  }
}

export function validateOrderStatus(order: OrderEntity) {
  if (
    order.status !== OrderStatus.CREATED &&
    order.status !== OrderStatus.PAID
  ) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
        fallbackMessage: `Order status must be 'created' or 'paid', but was '${order.status}'`,
      })
    );
  }
}

export function validateGiftOrderCollectionMatch(
  gift: GiftEntity,
  order: OrderEntity
): void {
  if (gift.collectionId !== order.collectionId) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: GIFT_ERRORS.GIFT_COLLECTION_MISMATCH,
        fallbackMessage: "Gift and order must be from the same collection",
      })
    );
  }
}

// Comprehensive validation function
export function validateLinkGiftToOrder({
  orderDoc,
  gift,
  user,
  userId,
}: {
  orderDoc: admin.firestore.DocumentSnapshot;
  gift: GiftEntity;
  user: UserEntity | null;
  userId: string;
}): OrderEntity {
  // Validate order exists and extract data
  const order = validateOrderExists(orderDoc);

  validateGiftOwnership(user, gift);
  validateOrderHasSeller(order);
  validateUserIsOrderSeller(order, userId);
  validateOrderStatus(order);
  validateGiftOrderCollectionMatch(gift, order);

  return order;
}

export function handleLinkGiftToOrderError(error: unknown) {
  LinkGiftToOrderLogger.logLinkGiftToOrderError(error);

  if (error instanceof HttpsError) {
    return {
      success: false,
      message: error.message,
    };
  }

  return {
    success: false,
    message: "An unexpected error occurred while linking gift to order",
  };
}

interface LinkGiftToOrderRequest {
  giftId: string;
  orderId: string;
}

export const validateLinkGiftToOrderRequestParams = (
  request: CallableRequest
) => {
  if (!request.data) {
    throw new HttpsError("invalid-argument", "Request data is required");
  }

  if (!request.auth?.uid) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  const { giftId, orderId } = request.data as LinkGiftToOrderRequest;

  if (!giftId || !orderId) {
    throw new HttpsError("invalid-argument", "giftId and orderId are required");
  }

  return { giftId, orderId };
};
