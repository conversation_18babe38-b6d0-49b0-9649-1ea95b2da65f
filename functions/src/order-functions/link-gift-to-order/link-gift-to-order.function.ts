import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import { LinkGiftToOrderLogger } from "./link-gift-to-order.logger";
import { linkGiftToOrderService } from "./link-gift-to-order.service";
import {
  handleLinkGiftToOrderError,
  validateLinkGiftToOrderRequestParams,
} from "./link-gift-to-order.validator";
import { requireAuth } from "../../services/auth-service/auth.service";

export const linkGiftToOrder = onCall<{
  giftId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  try {
    LinkGiftToOrderLogger.logLinkGiftToOrderFunctionCalled({
      giftId: request.data?.giftId,
      orderId: request.data?.orderId,
      userId: request.auth?.uid,
    });

    requireAuth(request);

    const { giftId, orderId } = validateLinkGiftToOrderRequestParams(request);

    const result = await linkGiftToOrderService({
      giftId,
      orderId,
      userId: request.auth.uid,
    });

    LinkGiftToOrderLogger.logLinkGiftToOrderCompleted({
      giftId,
      orderId,
      userId: request.auth.uid,
      success: result.success,
    });

    return result;
  } catch (error) {
    return handleLinkGiftToOrderError(error);
  }
});
