import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import { CreateSellOrderFromGiftLogger } from "./create-sell-order-from-gift.logger";
import { createSellOrderFromGiftFlow } from "./create-sell-order-from-gift.service";
import {
  handleCreateSellOrderFromGiftError,
  validateSellOrderRequestParams,
} from "./create-sell-order-from-gift.validator";
import { requireAuth } from "../../services/auth-service/auth.service";

export const createSellOrderFromGift = onCall<{
  giftId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  try {
    requireAuth(request);
    const { giftId, price } = validateSellOrderRequestParams(request);

    const result = await createSellOrderFromGiftFlow({
      giftId,
      price,
      userId: request.auth.uid,
    });

    CreateSellOrderFromGiftLogger.logCreateSellOrderFromGiftCompleted({
      giftId,
      price,
      userId: request.auth.uid,
      success: result.success,
      orderId: result.order?.id,
    });

    return result;
  } catch (error) {
    return handleCreateSellOrderFromGiftError(error);
  }
});
