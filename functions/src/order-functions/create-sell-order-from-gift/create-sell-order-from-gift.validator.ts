import { CallableRequest, HttpsError } from "firebase-functions/v2/https";
import { CreateSellOrderFromGiftLogger } from "./create-sell-order-from-gift.logger";
import { validateUserExists } from "../../services/auth-service/auth.validator";
import { GiftEntity } from "../../marketplace-shared";

export function handleCreateSellOrderFromGiftError(error: unknown) {
  CreateSellOrderFromGiftLogger.logCreateSellOrderFromGiftError(error);

  if (error instanceof HttpsError) {
    return {
      success: false,
      message: error.message,
    };
  }

  return {
    success: false,
    message: "An unexpected error occurred while creating sell order from gift",
  };
}

export const validateSellOrderRequestParams = (
  request: CallableRequest<{
    giftId: string;
    price: number;
  }>
) => {
  if (!request.data) {
    throw new HttpsError("invalid-argument", "Request data is required");
  }

  if (!request.auth?.uid) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  const { giftId, price } = request.data;

  if (!giftId || !price) {
    throw new HttpsError("invalid-argument", "giftId and price are required");
  }

  if (price <= 0) {
    throw new HttpsError("invalid-argument", "Price must be greater than 0");
  }

  return { giftId, price };
};

export const validateUserIsGiftOwner = async (
  gift: GiftEntity,
  userId: string
) => {
  const user = await validateUserExists(userId);
  if (!user || user.tg_id !== gift.owner_tg_id) {
    throw new HttpsError(
      "permission-denied",
      "Gift does not belong to the current user"
    );
  }
};
