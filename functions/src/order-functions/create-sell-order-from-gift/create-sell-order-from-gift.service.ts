import { validateCollectionExists } from "../../services/collection-service/collection.validator";
import {
  formatDateToFirebaseTimestamp,
  OrderEntity,
  OrderStatus,
} from "../../marketplace-shared";
import { validateCollectionIsActive } from "../../services/collection-service/collection-service";
import { getNextCounterValue } from "../../services/counter-service/counter-service";
import {
  DBOrdersCollection,
  getCurrentDateFirestoreTimestamp,
} from "../../services/db.service";
import { getAppConfig } from "../../services/fee-service/fee-service";
import { validateGiftForLinking } from "../../services/gift-service/gift-validator";
import { CreateSellOrderFromGiftLogger } from "./create-sell-order-from-gift.logger";
import { validateUserIsGiftOwner } from "./create-sell-order-from-gift.validator";

export async function createSellOrderFromGiftFlow({
  giftId,
  price,
  userId,
}: {
  giftId: string;
  price: number;
  userId: string;
}) {
  // 1. Validate gift (existence, status, not used in other orders)
  const gift = await validateGiftForLinking(giftId);

  // 2. Get user to validate gift ownership
  validateUserIsGiftOwner(gift, userId);

  // 4. Get and validate collection
  const collection = await validateCollectionExists(gift.collectionId);

  // 5. Validate collection is active and in correct status
  validateCollectionIsActive(collection);

  // 6. Get next order number from counters collection
  const orderNumber = await getNextCounterValue();

  // 7. Get fees from app config
  const appConfig = await getAppConfig();

  // 8. Create the order
  const orderRef = DBOrdersCollection.doc();
  const now = getCurrentDateFirestoreTimestamp();

  const orderEntity: OrderEntity = {
    id: orderRef.id,
    number: orderNumber,
    collectionId: gift.collectionId,
    sellerId: userId,
    price,
    status: OrderStatus.ACTIVE, // Order is immediately active since gift is attached
    gift_id_list: [giftId],
    createdAt: formatDateToFirebaseTimestamp(now),
    updatedAt: formatDateToFirebaseTimestamp(now),
    fees: {
      buyer_locked_percentage: appConfig.buyer_lock_percentage,
      seller_locked_percentage: appConfig.seller_lock_percentage,
      purchase_fee: appConfig.purchase_fee,
      referrer_fee: appConfig.referrer_fee,
      order_cancellation_fee: appConfig.cancel_order_fee,
      resell_purchase_fee: appConfig.resell_purchase_fee,
      resell_purchase_fee_for_seller: appConfig.resell_purchase_fee_for_seller,
    },
  };

  // 9. Create the order
  await orderRef.set(orderEntity);

  CreateSellOrderFromGiftLogger.logOrderCreated({
    orderId: orderRef.id,
    orderNumber,
    giftId,
    userId,
    price,
    collectionId: gift.collectionId,
  });

  return {
    success: true,
    message: "Sell order created successfully from gift.",
    order: orderEntity,
  };
}
