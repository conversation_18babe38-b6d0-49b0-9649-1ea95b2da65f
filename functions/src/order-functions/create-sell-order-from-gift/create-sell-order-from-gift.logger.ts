import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export const CreateSellOrderFromGiftLogger = {
  logCreateSellOrderFromGiftFunctionCalled(context: {
    giftId?: string;
    price?: number;
    userId?: string;
  }) {
    logger.info("Create sell order from gift function called", {
      operation: LogOperations.CREATE_SELL_ORDER_FROM_GIFT_START,
      giftId: context.giftId,
      price: context.price,
      userId: context.userId,
    });
  },

  logOrderCreated(context: {
    orderId: string;
    orderNumber: number;
    giftId: string;
    userId: string;
    price: number;
    collectionId: string;
  }) {
    logger.info("Sell order created from gift", {
      operation: LogOperations.ORDER_CREATED_FROM_GIFT,
      orderId: context.orderId,
      orderNumber: context.orderNumber,
      giftId: context.giftId,
      userId: context.userId,
      price: context.price,
      collectionId: context.collectionId,
    });
  },

  logCreateSellOrderFromGiftCompleted(context: {
    giftId: string;
    price: number;
    userId: string;
    success: boolean;
    orderId?: string;
  }) {
    logger.info("Create sell order from gift function completed", {
      operation: LogOperations.CREATE_SELL_ORDER_FROM_GIFT_COMPLETE,
      giftId: context.giftId,
      price: context.price,
      userId: context.userId,
      success: context.success,
      orderId: context.orderId,
    });
  },

  logCreateSellOrderFromGiftError(error: unknown) {
    logger.error("Error in create sell order from gift function", {
      operation: LogOperations.CREATE_SELL_ORDER_FROM_GIFT_ERROR,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
  },
};
