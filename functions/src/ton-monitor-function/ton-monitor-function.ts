import { onRequest } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../constants";
import { processWebhookTransaction } from "./ton-monitor-function.service";
import { TonMonitorLogger } from "./ton-monitor-function.logger";

// TON Console webhook endpoint
export const tonMonitor = onRequest(
  {
    ...commonFunctionsConfig,
    cors: true,
  },
  async (req, res) => {
    try {
      // Only allow POST requests
      if (req.method !== "POST") {
        res.status(405).json({ error: "Method not allowed" });
        return;
      }

      TonMonitorLogger.logWebhookReceived({
        status: "webhook_received",
        timestamp: new Date().toISOString(),
        body: req.body,
      });

      const result = await processWebhookTransaction(req.body);

      TonMonitorLogger.logWebhookProcessed({
        status: "webhook_processed",
        result,
      });

      res.status(200).json({ success: true, result });
    } catch (error) {
      TonMonitorLogger.logWebhookError({
        error,
        status: "webhook_failed",
      });

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
);
