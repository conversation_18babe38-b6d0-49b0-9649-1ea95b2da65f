import { getHttpEndpoint } from "@orbs-network/ton-access";
import fetch from "node-fetch";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../transaction-description-intl-keys";
import { isDevelopment } from "../config";
import { MIN_TRANSACTION_THRESHOLD_TON } from "../constants";
import { TxType, UserEntity } from "../marketplace-shared";
import { addFundsWithHistory } from "../services/balance-service/balance-service";
import { DBUserCollection } from "../services/db.service";
import {
  applyDepositFee,
  getAppConfig,
} from "../services/fee-service/fee-service";
import { extractRawTonAddress, roundToThreeDecimals } from "../utils";
import { TonMonitorLogger } from "./ton-monitor-function.logger";

// Webhook payload interface from TON Console
interface WebhookPayload {
  account_id: string;
  lt: number;
  tx_hash: string;
}

interface TonTransaction {
  transaction_id: {
    lt: string;
    hash?: string;
  };
  address: {
    account_address: string;
  };
  utime: number;
  in_msg?: {
    source?: string;
    value: string;
    msg_data?: {
      message?: string;
    };
  };
  out_msgs?: Array<{
    destination?: string;
    value: string;
  }>;
}

async function findUserByTonWallet(
  tonWalletAddress: string
): Promise<UserEntity | null> {
  const usersRef = DBUserCollection;

  TonMonitorLogger.logUserLookup({
    tonWalletAddress,
    operation: "user_lookup",
  });

  // First try exact match
  let query = usersRef.where("ton_wallet_address", "==", tonWalletAddress);
  let snapshot = await query.get();

  if (!snapshot.empty) {
    const doc = snapshot.docs[0];
    TonMonitorLogger.logUserFound({
      tonWalletAddress,
      userId: doc.id,
      operation: "user_lookup",
    });
    return {
      // @ts-expect-error note
      id: doc.id,
      ...doc.data(),
    };
  }

  const rawAddress = extractRawTonAddress(tonWalletAddress);
  if (!rawAddress) {
    TonMonitorLogger.logInvalidAddress({
      tonWalletAddress,
      operation: "user_lookup",
    });
    return null;
  }

  TonMonitorLogger.logRawAddressSearch({
    tonWalletAddress,
    rawAddress,
    operation: "user_lookup",
  });

  // Query directly by raw_ton_wallet_address field - much more efficient!
  const rawQuery = usersRef.where("raw_ton_wallet_address", "==", rawAddress);
  const rawSnapshot = await rawQuery.get();

  if (!rawSnapshot.empty) {
    const doc = rawSnapshot.docs[0];
    const userData = doc.data();
    TonMonitorLogger.logUserFoundByRaw({
      userTonWallet: userData.ton_wallet_address || "",
      searchedAddress: tonWalletAddress,
      userId: doc.id,
      operation: "user_lookup",
    });
    return {
      ...userData,
      id: doc.id,
    };
  }

  TonMonitorLogger.logUserNotFound({
    tonWalletAddress,
    operation: "user_lookup",
  });
  return null;
}

// Validate webhook payload from TON Console
function validateWebhookPayload(payload: any): WebhookPayload | null {
  if (!payload || typeof payload !== "object") {
    return null;
  }

  const { account_id, lt, tx_hash } = payload;

  if (!account_id || !lt || !tx_hash) {
    return null;
  }

  if (typeof account_id !== "string" || typeof tx_hash !== "string") {
    return null;
  }

  if (typeof lt !== "number" && typeof lt !== "string") {
    return null;
  }

  return {
    account_id,
    lt: typeof lt === "string" ? parseInt(lt) : lt,
    tx_hash,
  };
}

function extractTransactionInfo(tx: TonTransaction) {
  if (!tx?.in_msg?.source || !tx.in_msg.value) {
    return null;
  }

  const amount = roundToThreeDecimals(parseInt(tx.in_msg.value) / **********);
  const originalSender = tx.in_msg.source;

  TonMonitorLogger.logTransactionExtraction({
    sender: originalSender,
    amount,
    transactionId: tx.transaction_id.lt,
    operation: "transaction_extraction",
  });

  return {
    sender: originalSender, // Keep original address, flexible matching will handle it
    amount,
    message: tx.in_msg.msg_data?.message,
  };
}

async function fetchTransactionHash(params: {
  address: string;
  txHash: string;
  lt: number;
}): Promise<TonTransaction | null> {
  const { address, txHash, lt } = params;
  const network = isDevelopment() ? "testnet" : "mainnet";
  const endpoint = await getHttpEndpoint({ network });

  const requestBody = {
    id: "1",
    jsonrpc: "2.0",
    method: "getTransactions",
    params: {
      address: address,
      limit: 1,
      hash: txHash,
      archival: true,
      lt: Number(lt),
    },
  };

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  try {
    TonMonitorLogger.logTonApiCall({
      endpoint,
      txHash,
      operation: "webhook_transaction_fetch",
    });

    const response = await fetch(endpoint, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `TON API error: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(`TON API returned error: ${JSON.stringify(data.error)}`);
    }

    if (!data.ok || !data.result) {
      throw new Error(
        `TON API returned unsuccessful response: ${JSON.stringify(data)}`
      );
    }

    // Find transaction by hash
    const transactions = data.result || [];
    const targetTx = transactions[0];

    if (!targetTx) {
      TonMonitorLogger.logTransactionNotFound({
        txHash,
        operation: "webhook_transaction_fetch",
      });
      return null;
    }

    TonMonitorLogger.logTonApiCall({
      txHash,
      found: true,
      operation: "webhook_transaction_fetch",
    });

    return targetTx;
  } catch (error) {
    TonMonitorLogger.logTonApiError({
      error,
      txHash,
      operation: "webhook_transaction_fetch",
    });
    throw error;
  }
}

export async function processWebhookTransaction(payload: any) {
  try {
    TonMonitorLogger.logMonitorStatus({
      monitor: "ton_webhook",
      status: "started",
    });

    // Validate the webhook payload
    const validatedPayload = validateWebhookPayload(payload);

    if (!validatedPayload) {
      TonMonitorLogger.logWebhookInvalidPayload({
        payload,
        operation: "webhook_validation",
      });
      return { success: false, error: "Invalid webhook payload" };
    }

    const { account_id, lt, tx_hash } = validatedPayload;

    TonMonitorLogger.logWebhookValidation({
      accountId: account_id,
      lt: lt.toString(),
      txHash: tx_hash,
      operation: "webhook_validation",
    });

    // Fetch the specific transaction
    const transaction = await fetchTransactionHash({
      address: account_id,
      txHash: tx_hash,
      lt,
    });

    if (!transaction) {
      return { success: false, error: "Transaction not found" };
    }

    // Extract transaction info
    const txInfo = extractTransactionInfo(transaction);

    if (!txInfo) {
      TonMonitorLogger.logMonitorStatus({
        monitor: "ton_webhook",
        status: "no_incoming_transaction",
        txHash: tx_hash,
      });
      return { success: true, message: "No incoming transaction to process" };
    }

    const appConfig = await getAppConfig();

    // Check minimum threshold
    if (txInfo.amount <= appConfig.min_deposit_amount) {
      TonMonitorLogger.logMonitorStatus({
        monitor: "ton_webhook",
        status: "amount_below_threshold",
        amount: txInfo.amount,
        minThreshold: MIN_TRANSACTION_THRESHOLD_TON,
      });
      return { success: true, message: "Transaction amount below threshold" };
    }

    // Find user by sender address
    const user = await findUserByTonWallet(txInfo.sender);

    if (!user) {
      TonMonitorLogger.logWalletNotFound({
        walletAddress: txInfo.sender,
        transactionId: transaction.transaction_id.lt,
        operation: "webhook_processing",
      });
      return { success: true, message: "No user found for sender address" };
    }

    // Update user balance
    const amount = txInfo.amount;
    const netAmount = await applyDepositFee({ depositAmount: amount });

    await addFundsWithHistory({
      userId: user.id,
      amount: netAmount,
      txType: TxType.DEPOSIT,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.DEPOSIT_FROM_TON_WALLET,
      descriptionIntlParams: {
        originalAmount: amount.toString(),
        netAmount: netAmount.toString(),
      },
    });

    TonMonitorLogger.logTransactionProcessed({
      transactionHash: tx_hash,
      status: "processed",
    });

    TonMonitorLogger.logMonitorStatus({
      monitor: "ton_webhook",
      status: "completed",
      userId: user.id,
      amount: txInfo.amount,
    });

    return {
      success: true,
      message: "Transaction processed successfully",
      data: {
        userId: user.id,
        amount: txInfo.amount,
        txHash: tx_hash,
      },
    };
  } catch (error) {
    TonMonitorLogger.logMonitorError({ error });
    throw error;
  }
}
