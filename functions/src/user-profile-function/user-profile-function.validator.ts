import { HttpsError } from "firebase-functions/v2/https";
import { AUTH_ERRORS, GENERIC_ERRORS } from "../error-messages";

export function throwUnauthenticated(): never {
  throw new HttpsError(
    "unauthenticated",
    JSON.stringify({
      errorKey: AUTH_ERRORS.UNAUTHENTICATED,
      fallbackMessage: "Authentication required.",
    })
  );
}

export function throwUserNotFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: AUTH_ERRORS.USER_NOT_FOUND,
      fallbackMessage: "User not found.",
    })
  );
}

export function throwWalletAlreadyUsed(): never {
  throw new HttpsError(
    "already-exists",
    JSON.stringify({
      errorKey: AUTH_ERRORS.WALLET_ALREADY_USED,
      fallbackMessage:
        "This wallet address is already used in another account.",
    })
  );
}

export function throwUserProfileInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    JSON.stringify({
      errorKey: GENERIC_ERRORS.SERVER_ERROR,
      fallbackMessage: message ?? "Server error while updating user profile.",
    })
  );
}
