import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../constants";
import {
  getUserData,
  validateWalletAddress,
  prepareUpdateData,
  handleReferrerUpdate,
  updateUserProfile,
} from "./user-profile-function.service";
import { logProfileUpdateError } from "./user-profile-function.logger";
import {
  throwUnauthenticated,
  throwUserProfileInternalError,
} from "./user-profile-function.validator";

export const changeUserData = onCall<{
  name?: string;
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referrer_id?: string;
  points?: number;
}>(commonFunctionsConfig, async (request) => {
  if (!request.auth) {
    throwUnauthenticated();
  }

  const {
    name,
    tg_id,
    ton_wallet_address,
    raw_ton_wallet_address,
    referrer_id,
    points,
  } = request.data;
  const userId = request.auth.uid;

  try {
    // Get current user data
    const currentUserData = await getUserData(userId);

    // Prepare update data
    const updateData = prepareUpdateData({
      name,
      tg_id,
      ton_wallet_address,
      raw_ton_wallet_address,
      points,
    });

    // Validate wallet address if provided
    if (updateData.raw_ton_wallet_address) {
      await validateWalletAddress(userId, updateData.raw_ton_wallet_address);
    }

    // Handle referrer update if provided
    if (referrer_id) {
      await handleReferrerUpdate(userId, referrer_id, currentUserData);

      // Add referrer_id to update data if user doesn't have one
      if (!currentUserData.referrer_id) {
        updateData.referrer_id = referrer_id;
      }
    }

    // Update user profile
    const { updatedFields } = await updateUserProfile(userId, updateData);

    return {
      success: true,
      message: "User profile updated successfully",
      updatedFields,
    };
  } catch (error) {
    logProfileUpdateError({
      error,
      userId,
      requestData: request.data,
    });

    if (error instanceof HttpsError) {
      throw error;
    }

    throwUserProfileInternalError((error as any).message);
  }
});
