import { UserEntity } from "../marketplace-shared";
import { DBUserCollection } from "../services/db.service";
import { extractRawTonAddress, prepareUserDataForSave } from "../utils";
import { getUserPoints } from "../utils/referral-points";
import {
  throwUserNotFound,
  throwWalletAlreadyUsed,
} from "./user-profile-function.validator";
import {
  logProfileUpdated,
  logReferrerNotFound,
  logReferrerPointsError,
  logReferrerPointsUpdated,
  logReferrerSet,
  logReferrerUpdateSkipped,
} from "./user-profile-function.logger";

export interface ChangeUserDataParams {
  name?: string;
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referrer_id?: string;
  points?: number;
}

export interface ChangeUserDataResult {
  success: boolean;
  message: string;
  updatedFields: string[];
}

export async function getUserData(userId: string): Promise<UserEntity> {
  const userDoc = await DBUserCollection.doc(userId).get();

  if (!userDoc.exists) {
    throwUserNotFound();
  }

  return userDoc.data() as UserEntity;
}

export async function validateWalletAddress(
  userId: string,
  rawTonWalletAddress: string
) {
  const existingUserQuery = await DBUserCollection.where(
    "raw_ton_wallet_address",
    "==",
    rawTonWalletAddress
  ).get();

  if (!existingUserQuery.empty) {
    const existingUserDoc = existingUserQuery.docs[0];
    const existingUserId = existingUserDoc.id;

    if (existingUserId !== userId) {
      throwWalletAlreadyUsed();
    }
  }
}

export function prepareUpdateData(params: ChangeUserDataParams) {
  const { name, tg_id, ton_wallet_address, raw_ton_wallet_address, points } =
    params;

  const updateData: Partial<UserEntity> = {};

  if (name !== undefined) {
    updateData.displayName = name;
  }

  if (tg_id !== undefined) {
    updateData.tg_id = tg_id;
  }

  if (ton_wallet_address !== undefined) {
    updateData.ton_wallet_address = ton_wallet_address;

    if (raw_ton_wallet_address === undefined && ton_wallet_address) {
      const rawAddress = extractRawTonAddress(ton_wallet_address);
      if (rawAddress) {
        updateData.raw_ton_wallet_address = rawAddress;
      }
    }
  }

  if (raw_ton_wallet_address !== undefined) {
    updateData.raw_ton_wallet_address = raw_ton_wallet_address;
  }

  if (points !== undefined) {
    updateData.points = points;
  }

  return updateData;
}

export async function handleReferrerUpdate(
  userId: string,
  referrerId: string,
  currentUserData: UserEntity
) {
  if (currentUserData.referrer_id) {
    logReferrerUpdateSkipped({
      userId,
      existingReferrerId: currentUserData.referrer_id,
      attemptedReferrerId: referrerId,
    });
    return;
  }

  logReferrerSet(userId, referrerId);

  try {
    const referralsQuery = await DBUserCollection.where(
      "referrer_id",
      "==",
      referrerId
    ).get();

    const referralCount = referralsQuery.size;

    const referrerQuery = await DBUserCollection.doc(referrerId).get();

    if (referrerQuery.exists) {
      const referrerData = referrerQuery.data() as UserEntity;
      const currentPoints = referrerData.points ?? 0;
      const newPoints = getUserPoints(referralCount);

      await DBUserCollection.doc(referrerId).update({
        points: newPoints,
      });

      logReferrerPointsUpdated({
        referrerId,
        referralCount,
        previousPoints: currentPoints,
        newPoints,
      });
    } else {
      logReferrerNotFound({ referrerId });
    }
  } catch (pointsError) {
    logReferrerPointsError({
      error: pointsError,
      referrerId,
      userId,
    });
  }
}

export async function updateUserProfile(
  userId: string,
  updateData: Partial<UserEntity>
) {
  const preparedData = prepareUserDataForSave(updateData);

  await DBUserCollection.doc(userId).update(preparedData);

  logProfileUpdated({
    userId,
    updatedFields: Object.keys(preparedData),
    preparedData,
  });

  return { updatedFields: Object.keys(preparedData) };
}
