import { onCall } from "firebase-functions/v2/https";
import {
  requireAdminRole,
  requireAuth,
} from "../services/auth-service/auth.service";
import { commonFunctionsConfig, LogOperations } from "../constants";
import { AdminCollectionLogger } from "./admin-collection.logger";
import {
  clearOrderDeadlines,
  recalculateOrderDeadlines,
} from "./admin-collection.service";

export const recalculateDeadlines = onCall<{
  collectionId: string;
}>(commonFunctionsConfig, async (request) => {
  requireAuth(request);

  const { collectionId } = request.data;
  const userId = request.auth.uid;

  try {
    await requireAdminRole(userId);
    const updatedCount = await recalculateOrderDeadlines(collectionId);

    AdminCollectionLogger.logAdminCollectionOperation({
      operation: LogOperations.RECALCULATE_DEADLINES,
      collectionId,
      updatedCount,
      userId,
    });

    return {
      success: true,
      message: `Successfully recalculated deadlines for ${updatedCount} orders`,
      updatedCount,
    };
  } catch (error) {
    AdminCollectionLogger.logAdminCollectionError({
      error,
      operation: LogOperations.RECALCULATE_DEADLINES,
      collectionId,
      userId,
    });

    throw error;
  }
});

export const clearDeadlines = onCall<{
  collectionId: string;
}>(commonFunctionsConfig, async (request) => {
  requireAuth(request);

  const { collectionId } = request.data;
  const userId = request.auth.uid;

  try {
    await requireAdminRole(userId);
    const updatedCount = await clearOrderDeadlines(collectionId);

    AdminCollectionLogger.logAdminCollectionOperation({
      operation: LogOperations.CLEAR_DEADLINES,
      collectionId,
      updatedCount,
      userId,
    });

    return {
      success: true,
      message: `Successfully cleared deadlines for ${updatedCount} orders`,
      updatedCount,
    };
  } catch (error) {
    AdminCollectionLogger.logAdminCollectionError({
      error,
      operation: LogOperations.CLEAR_DEADLINES,
      collectionId,
      userId,
    });

    throw error;
  }
});
