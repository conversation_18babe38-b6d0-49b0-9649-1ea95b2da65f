import * as admin from "firebase-admin";
import { OrderStatus } from "../marketplace-shared";
import { db, DBOrdersCollection } from "../services/db.service";
import { calculateOrderDeadline } from "../services/deadline-service/deadline-service";
import { validateCollectionExists } from "../services/collection-service/collection.validator";

export async function batchUpdateOrders(
  orders: admin.firestore.QueryDocumentSnapshot[],
  updateFields: Record<string, any>
) {
  const BATCH_SIZE = 400;
  let totalUpdatedCount = 0;

  for (let i = 0; i < orders.length; i += BATCH_SIZE) {
    const chunk = orders.slice(i, i + BATCH_SIZE);
    const batch = db.batch();

    for (const orderDoc of chunk) {
      batch.update(orderDoc.ref, {
        ...updateFields,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
    }

    await batch.commit();
    totalUpdatedCount += chunk.length;
  }

  return totalUpdatedCount;
}

export async function recalculateOrderDeadlines(collectionId: string) {
  const collection = await validateCollectionExists(collectionId);

  const ordersQuery = await DBOrdersCollection.where(
    "collectionId",
    "==",
    collectionId
  )
    .where("status", "in", [OrderStatus.ACTIVE, OrderStatus.PAID])
    .get();

  if (ordersQuery.empty) {
    return 0;
  }

  const orders = ordersQuery.docs;
  const updatePromises = orders.map(async (orderDoc) => {
    const newDeadline = await calculateOrderDeadline(collection);

    return {
      ref: orderDoc.ref,
      updateData: { deadline: newDeadline },
    };
  });

  const updates = await Promise.all(updatePromises);

  return batchUpdateOrders(orders, {
    deadline: updates[0]?.updateData.deadline,
  });
}

export async function clearOrderDeadlines(collectionId: string) {
  await validateCollectionExists(collectionId);

  const ordersQuery = await DBOrdersCollection.where(
    "collectionId",
    "==",
    collectionId
  )
    .where("status", "in", [OrderStatus.ACTIVE, OrderStatus.PAID])
    .get();

  if (ordersQuery.empty) {
    return 0;
  }

  return batchUpdateOrders(ordersQuery.docs, {
    deadline: admin.firestore.FieldValue.delete(),
  });
}
