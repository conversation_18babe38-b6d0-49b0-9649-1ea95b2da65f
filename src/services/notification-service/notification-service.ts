import { botMessages } from "../../intl/messages";
import { PREM_RELAYER_USERNAME } from "../../app.constants";
import { sendNotificationToUser } from "../shared/notification-base.service";
import {
  NotificationType,
  NotificationResult,
} from "../../types/notification-types";

export async function notifySellerOrderPaid(params: {
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  price?: number;
}): Promise<{ success: boolean; message: string }> {
  const { orderId, sellerId, orderNumber, price } = params;

  return sendNotificationToUser(
    {
      orderId,
      userId: sellerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.sellerOrderPaidWithLink.id,
      params: {
        price: price ? price.toString() : "N/A",
      },
    },
    "seller"
  );
}

export async function notifyBuyerGiftSent(params: {
  orderId: string;
  buyerId: string;
  orderNumber?: number;
}) {
  const { orderId, buyerId, orderNumber } = params;

  return sendNotificationToUser(
    {
      orderId,
      userId: buyerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.buyerGiftSentWithLink.id,
      params: {
        relayerUsername: PREM_RELAYER_USERNAME.replace("@", ""),
      },
    },
    "buyer"
  );
}

export async function notifySellerNewProposal(params: {
  orderId: string;
  sellerId: string;
  proposedPrice: number;
  originalPrice: number;
  orderNumber?: number;
}) {
  const { orderId, sellerId, proposedPrice, originalPrice, orderNumber } =
    params;

  return sendNotificationToUser(
    {
      orderId,
      userId: sellerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.newProposalReceived.id,
      params: {
        proposedPrice: proposedPrice.toString(),
        originalPrice: originalPrice.toString(),
      },
    },
    "seller"
  );
}

export async function notifyProposerAccepted(params: {
  orderId: string;
  proposerId: string;
  proposedPrice: number;
  orderNumber?: number;
}) {
  const { orderId, proposerId, proposedPrice, orderNumber } = params;

  return sendNotificationToUser(
    {
      orderId,
      userId: proposerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.proposalAccepted.id,
      params: {
        proposedPrice: proposedPrice.toString(),
      },
    },
    "buyer"
  );
}

/**
 * Unified notification processor that handles all notification types
 */
export async function processUnifiedNotification(
  payload: any
): Promise<NotificationResult> {
  try {
    if (!payload.notification_type) {
      return {
        success: false,
        message: "Missing notification_type field",
      };
    }

    switch (payload.notification_type) {
      case NotificationType.SELLER_ORDER_PAID:
        return await notifySellerOrderPaid({
          orderId: payload.orderId,
          sellerId: payload.sellerId,
          orderNumber: payload.orderNumber,
          price: payload.price,
        });

      case NotificationType.BUYER_GIFT_SENT:
        return await notifyBuyerGiftSent({
          orderId: payload.orderId,
          buyerId: payload.buyerId,
          orderNumber: payload.orderNumber,
        });

      case NotificationType.SELLER_NEW_PROPOSAL:
        return await notifySellerNewProposal({
          orderId: payload.orderId,
          sellerId: payload.sellerId,
          proposedPrice: payload.proposedPrice,
          originalPrice: payload.originalPrice,
          orderNumber: payload.orderNumber,
        });

      case NotificationType.PROPOSER_ACCEPTED:
        return await notifyProposerAccepted({
          orderId: payload.orderId,
          proposerId: payload.proposerId,
          proposedPrice: payload.proposedPrice,
          orderNumber: payload.orderNumber,
        });

      default:
        return {
          success: false,
          message: `Unknown notification type: ${payload.notification_type}`,
        };
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to process notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}
