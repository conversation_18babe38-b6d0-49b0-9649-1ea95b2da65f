import { LogOperations } from "../../app.constants";
import { log } from "../../utils/logger";

export const NotificationServiceLogger = {
  logSellerNotificationStart(params: any) {
    log.info("Notifying seller of order payment", {
      operation: LogOperations.NOTIFY_SELLER_ORDER_PAID,
      ...params,
    });
  },

  logSellerNotificationSuccess(params: any) {
    log.info("Seller notification sent successfully", {
      operation: LogOperations.NOTIFY_SELLER_ORDER_PAID,
      ...params,
    });
  },

  logSellerNotificationError(error: unknown, params: any) {
    log.error("Failed to notify seller of order payment", error, {
      operation: LogOperations.NOTIFY_SELLER_ORDER_PAID,
      ...params,
    });
  },

  logBuyerNotificationStart(params: any) {
    log.info("Notifying buyer of gift sent", {
      operation: LogOperations.NOTIFY_BUYER_GIFT_SENT,
      ...params,
    });
  },

  logBuyerNotificationSuccess(params: any) {
    log.info("Buyer notification sent successfully", {
      operation: LogOperations.NOTIFY_BUYER_GIFT_SENT,
      ...params,
    });
  },

  logBuyerNotificationError(error: unknown, params: any) {
    log.error("Failed to notify buyer of gift sent", error, {
      operation: LogOperations.NOTIFY_BUYER_GIFT_SENT,
      ...params,
    });
  },
};
