import { log } from "../../utils/logger";

export const HealthcheckLogger = {
  logHealthStatusCheckError(params: { error: unknown }) {
    log.error("Failed to check health status", params.error, {
      operation: "healthcheck_status",
    });
  },

  logBotStartTimeUpdated(params: { timestamp: string }) {
    log.healthLog("Bot start time updated", {
      status: "bot_start_time_set",
      timestamp: params.timestamp,
    });
  },
};
