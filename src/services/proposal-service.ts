import { proposalsTableMessages } from '@/components/order-details/proposals/intl/proposals-table.messages';
import type {
  OrderEntity,
  ProposalEntity,
  UserEntity,
} from '@/mikerudenko/marketplace-shared';
import { ProposalStatus } from '@/mikerudenko/marketplace-shared';

export const PROPOSAL_STATUS_MESSAGE_MAP = {
  [ProposalStatus.ACTIVE]: proposalsTableMessages.statusActive,
  [ProposalStatus.CANCELLED]: proposalsTableMessages.statusCancelled,
  [ProposalStatus.ACCEPTED]: proposalsTableMessages.statusAccepted,
} as const;

export const getProposalStatusMessageKey = (status: ProposalStatus) => {
  return PROPOSAL_STATUS_MESSAGE_MAP[status] || status;
};

export const PROPOSAL_STATUS_COLORS: Record<ProposalStatus, string> = {
  [ProposalStatus.ACTIVE]: 'text-[var(--color-status-green)]',
  [ProposalStatus.CANCELLED]: 'text-[var(--color-status-red)]',
  [ProposalStatus.ACCEPTED]: 'text-[var(--color-status-blue)]',
};

export function getProposalStatusColor(status: ProposalStatus): string {
  return PROPOSAL_STATUS_COLORS[status] || 'text-muted-foreground';
}

export function isUserSeller(
  order: OrderEntity,
  currentUser: UserEntity | null,
): boolean {
  return order.sellerId === currentUser?.id;
}

export function isUserProposer(
  proposal: ProposalEntity,
  currentUser: UserEntity | null,
): boolean {
  return proposal.proposer_id === currentUser?.id;
}
