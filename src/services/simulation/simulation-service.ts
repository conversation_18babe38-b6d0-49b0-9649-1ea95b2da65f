import { Context } from "telegraf";
import { APP_NAME } from "../../app.constants";
import { T } from "../../i18n";
import { botMessages } from "../../intl/messages";
import { createMarketplaceInlineKeyboard } from "../../utils/keyboards";
import { createGiftDirectly } from "../gift-service/gift.service";
import { withdrawGift } from "../gift-service/gift-withdrawal.service";
import { generateMockGift } from "../gift-service/mock-gift.service";
import { clearUserSession } from "../session-service/session-service";
import { SimulationServiceLogger } from "./simulation-service.logger";

export interface SimulationConfig {
  isEnabled: boolean;
  defaultCollectionId: string;
}

export interface OrderSimulationParams {
  ctx: Context;
  order: any;
  orderId: string;
  isPaidOrderAwaitingGift: boolean;
  isCreatedOrderNeedingActivation: boolean;
  isBuyer: boolean;
  isSeller: boolean;
}

export interface GiftDepositSimulationParams {
  ctx: Context;
  tgId: string;
}

export interface GiftWithdrawalSimulationParams {
  ctx: Context;
  tgId: string;
  withdrawalGiftId: string;
}

export class SimulationService {
  private readonly config: SimulationConfig;

  constructor(config: SimulationConfig) {
    this.config = config;
  }

  isSimulationEnabled(): boolean {
    return this.config.isEnabled;
  }

  async handleGiftDepositSimulation(
    params: GiftDepositSimulationParams
  ): Promise<void> {
    if (!this.config.isEnabled) {
      throw new Error("Simulation is not enabled");
    }

    const { ctx, tgId } = params;

    // Clear user session
    await this.clearUserSessionSafely(tgId);

    const message = T(ctx, botMessages.simulationGiftDepositMode.id);
    await ctx.reply(message);

    try {
      const mockGift = generateMockGift();
      const result = await createGiftDirectly({
        gift: mockGift,
        userTgId: tgId,
        collectionId: this.config.defaultCollectionId,
      });

      if (result.success) {
        const successMessage = this.formatGiftDepositSuccessMessage(
          mockGift,
          ctx
        );
        await ctx.reply(successMessage, createMarketplaceInlineKeyboard(ctx));

        SimulationServiceLogger.logMockGiftDepositSuccess({
          tgId,
          giftId: result.giftId,
          collectionId: this.config.defaultCollectionId,
        });
      } else {
        await this.handleGiftDepositError(ctx, result.message);
      }
    } catch (error) {
      await this.handleGiftDepositError(ctx, "Failed to deposit mock gift");
      SimulationServiceLogger.logMockGiftDepositError({ error, tgId });
    }
  }

  async handleGiftWithdrawalSimulation(
    params: GiftWithdrawalSimulationParams
  ): Promise<void> {
    if (!this.config.isEnabled) {
      throw new Error("Simulation is not enabled");
    }

    const { ctx, tgId, withdrawalGiftId } = params;

    const message = T(ctx, botMessages.simulationGiftWithdrawalMode.id);
    await ctx.reply(message);

    try {
      const result = await withdrawGift({
        giftId: withdrawalGiftId,
        userTgId: tgId,
      });

      if (result.success) {
        const successMessage = T(
          ctx,
          botMessages.simulationGiftWithdrawalSuccess.id
        );
        await ctx.reply(successMessage, createMarketplaceInlineKeyboard());

        SimulationServiceLogger.logGiftWithdrawalSuccess({
          tgId,
          withdrawalGiftId,
          // @ts-ignore
          ownedGiftId: (result?.ownedGiftId as string) || "",
        });
      } else {
        const errorMessage = T(
          ctx,
          botMessages.simulationGiftWithdrawalError.id,
          {
            errorMessage: result.error || "Unknown error",
          }
        );
        await ctx.reply(errorMessage, createMarketplaceInlineKeyboard());

        SimulationServiceLogger.logGiftWithdrawalFailed({
          tgId,
          withdrawalGiftId,
          error: result.error as string,
        });
      }
    } catch (error) {
      const errorMessage = T(
        ctx,
        botMessages.simulationGiftWithdrawalError.id,
        {
          errorMessage: "Failed to process gift withdrawal in simulation mode",
        }
      );
      await ctx.reply(errorMessage, createMarketplaceInlineKeyboard());

      SimulationServiceLogger.logGiftWithdrawalError({
        error,
        tgId,
        withdrawalGiftId,
      });
    }
  }

  private async clearUserSessionSafely(tgId: string): Promise<void> {
    try {
      await clearUserSession(tgId);
    } catch (error) {
      SimulationServiceLogger.logClearSessionWarning({
        tgId,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private formatGiftDepositSuccessMessage(mockGift: any, ctx?: any): string {
    return `✅ Mock gift deposited successfully!\n\n${T(
      ctx,
      botMessages.simulationGiftDepositSuccess.id,
      {
        giftName: mockGift.base_name,
        modelName: mockGift.model.name,
        symbolName: mockGift.symbol.name,
        backdropName: mockGift.backdrop.name,
        APP_NAME,
      }
    )}`;
  }

  private async handleGiftDepositError(
    ctx: Context,
    errorMessage?: string
  ): Promise<void> {
    const message = T(ctx, botMessages.simulationGiftDepositError.id, {
      errorMessage: errorMessage || "Unknown error",
    });

    await ctx.reply(message, createMarketplaceInlineKeyboard(ctx));
  }
}
