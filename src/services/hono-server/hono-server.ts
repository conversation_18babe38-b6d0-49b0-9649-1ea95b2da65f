import { Hono } from "hono";
import { serve } from "@hono/node-server";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { HealthcheckService } from "../healthcheck-service/healthcheck-service";
import bot from "../../bot";
import { HonoServerLogger } from "./hono-server.logger";
import { processUnifiedNotification } from "../notification-service/notification-service";

const AUTH_TOKEN = process.env.AUTH_TOKEN;

// Endpoints map for API documentation
const ENDPOINTS = {
  PROTECTED: ["/healthcheck", "/notify"],
  PUBLIC: ["/readiness", "/webhook", "/"],
} as const;

const ALL_ENDPOINTS = [...ENDPOINTS.PROTECTED, ...ENDPOINTS.PUBLIC];

// Bearer token authentication middleware
const bearerAuth = async (c: any, next: any) => {
  const authHeader = c.req.header("Authorization");

  if (!authHeader?.startsWith("Bearer ")) {
    return c.json({ error: "Unauthorized: Bearer token required" }, 401);
  }

  const token = authHeader.substring(7);
  if (!AUTH_TOKEN || token !== AUTH_TOKEN) {
    return c.json({ error: "Unauthorized: Invalid token" }, 401);
  }

  await next();
};

let isReady = false;

export function setServerReady(ready: boolean): void {
  isReady = ready;
  HonoServerLogger.logServerReadiness({
    ready,
  });
}

export function createHonoApp() {
  const app = new Hono();

  // Add middleware
  app.use(
    "*",
    cors({
      origin: "*",
      allowMethods: ["GET", "POST", "OPTIONS"],
      allowHeaders: ["Content-Type", "Authorization"],
    })
  );

  app.use(
    "*",
    logger((message) => {
      HonoServerLogger.logHttpRequest({
        method: message.split(" ")[0],
        url: message.split(" ")[1],
      });
    })
  );

  // Security headers middleware
  app.use("*", async (c, next) => {
    await next();
    c.header("X-Frame-Options", "DENY");
    c.header("X-Content-Type-Options", "nosniff");
    c.header("X-XSS-Protection", "1; mode=block");

    const proto = c.req.header("X-Forwarded-Proto");
    if (proto === "https") {
      c.header(
        "Strict-Transport-Security",
        "max-age=31536000; includeSubDomains"
      );
    }
  });

  // Protected routes (require authentication)
  app.get("/healthcheck", bearerAuth, async (c) => {
    try {
      const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
      const isHealthy = await HealthcheckService.isHealthy();

      const response = {
        status: isHealthy ? "healthy" : "unhealthy",
        lastHealthcheck,
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      return c.json(response, isHealthy ? 200 : 503);
    } catch (error) {
      HonoServerLogger.logHealthcheckError({
        error,
      });

      const errorResponse = {
        status: "error",
        message: "Failed to check health status",
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      return c.json(errorResponse, 500);
    }
  });

  app.post("/notify", bearerAuth, async (c) => {
    try {
      const body = await c.req.json();
      const result = await processUnifiedNotification(body);

      return c.json(result, result.success ? 200 : 500);
    } catch (error) {
      HonoServerLogger.logWebhookProcessingError({ error });

      return c.json(
        {
          success: false,
          message: `Failed to process notification: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        },
        500
      );
    }
  });

  // Public routes
  app.get("/readiness", async (c) => {
    const response = {
      status: isReady ? "ready" : "not ready",
      timestamp: new Date().toISOString(),
      service: "marketplace-bot",
    };

    return c.json(response, isReady ? 200 : 503);
  });

  app.post("/webhook", async (c) => {
    try {
      const update = await c.req.json();

      HonoServerLogger.logWebhookReceived({
        updateId: update.update_id,
      });

      await bot.handleUpdate(update);

      return c.json({ ok: true });
    } catch (error) {
      HonoServerLogger.logWebhookProcessingError({
        error,
      });
      return c.json({ ok: false, error: "Failed to process update" }, 500);
    }
  });

  app.get("/", async (c) => {
    const response = {
      service: "marketplace-bot",
      status: "running",
      ready: isReady,
      timestamp: new Date().toISOString(),
      endpoints: ALL_ENDPOINTS,
    };

    return c.json(response);
  });

  // 404 handler
  app.notFound((c) => {
    const notFoundResponse = {
      error: "Not Found",
      message: `Route ${c.req.url} not found`,
      timestamp: new Date().toISOString(),
    };

    return c.json(notFoundResponse, 404);
  });

  return app;
}

export function startHonoServer(port: number = 8080): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      const app = createHonoApp();

      serve({
        fetch: app.fetch,
        port,
      });

      HonoServerLogger.logServerStarted({
        port,
      });

      resolve();
    } catch (error) {
      HonoServerLogger.logServerError({
        error,
        port,
      });
      reject(error);
    }
  });
}
