import { LogOperations } from "../../app.constants";
import { log } from "../../utils/logger";

export const GiftServiceLogger = {
  logGiftCreated(params: {
    giftId: string;
    ownerTgId: string;
    collectionId: string;
    isDirect?: boolean;
  }) {
    log.info("Gift created successfully", {
      operation: LogOperations.CREATE_GIFT,
      giftId: params.giftId,
      ownerTgId: params.ownerTgId,
      collectionId: params.collectionId,
      isDirect: params.isDirect || false,
    });
  },

  logGiftCreationError(params: {
    error: unknown;
    ownerTgId: string;
    collectionId: string;
    isDirect?: boolean;
  }) {
    log.error("Failed to create gift", params.error, {
      operation: LogOperations.CREATE_GIFT,
      ownerTgId: params.ownerTgId,
      collectionId: params.collectionId,
      isDirect: params.isDirect || false,
    });
  },

  logDirectGiftDepositStarted(params: {
    userTgId: string;
    collectionId: string;
    giftModel: string;
  }) {
    log.info("Starting direct gift deposit", {
      operation: LogOperations.DIRECT_GIFT_DEPOSIT,
      userTgId: params.userTgId,
      collectionId: params.collectionId,
      giftModel: params.giftModel,
    });
  },

  logDirectGiftDepositSuccess(params: {
    giftId: string;
    userTgId: string;
    collectionId: string;
  }) {
    log.info("Direct gift deposit completed successfully", {
      operation: LogOperations.DIRECT_GIFT_DEPOSIT,
      giftId: params.giftId,
      userTgId: params.userTgId,
      collectionId: params.collectionId,
      success: true,
    });
  },

  logDirectGiftDepositError(params: {
    error: unknown;
    userTgId: string;
    collectionId: string;
  }) {
    log.error("Failed to process direct gift deposit", params.error, {
      operation: LogOperations.DIRECT_GIFT_DEPOSIT,
      userTgId: params.userTgId,
      collectionId: params.collectionId,
      success: false,
    });
  },

  logGiftOwnershipUpdated(params: {
    giftId: string;
    newOwnerTgId: string;
    status: string;
  }) {
    log.info("Gift ownership updated successfully", {
      operation: LogOperations.UPDATE_GIFT_OWNERSHIP,
      giftId: params.giftId,
      newOwnerTgId: params.newOwnerTgId,
      status: params.status,
    });
  },

  logGiftOwnershipUpdateError(params: {
    error: unknown;
    giftId: string;
    newOwnerTgId: string;
    status: string;
  }) {
    log.error("Failed to update gift ownership", params.error, {
      operation: LogOperations.UPDATE_GIFT_OWNERSHIP,
      giftId: params.giftId,
      newOwnerTgId: params.newOwnerTgId,
      status: params.status,
    });
  },
};
