import { log } from "../../utils/logger";

export const GiftQueryLogger = {
  logGiftQueryStart(params: { tg_id: string; operation: string }) {
    log.info("Getting user gifts available for withdrawal", {
      operation: params.operation,
      tg_id: params.tg_id,
    });
  },

  logGiftsFound(params: {
    tg_id: string;
    totalGifts: number;
    operation: string;
  }) {
    log.info("Found gifts for user", {
      tg_id: params.tg_id,
      totalGifts: params.totalGifts,
      operation: params.operation,
    });
  },

  logGiftNotLinked(params: { giftId: string; operation: string }) {
    log.debug("Gift not linked to any order", {
      giftId: params.giftId,
      operation: params.operation,
    });
  },

  logGiftHasActiveOrder(params: { giftId: string; operation: string }) {
    log.debug("Gift has active order, not available for withdrawal", {
      giftId: params.giftId,
      operation: params.operation,
    });
  },

  logGiftEligibleForBuyer(params: {
    giftId: string;
    orderId?: string | undefined;
    operation: string;
  }) {
    log.debug("Gift linked to gift_sent_to_relayer order where user is buyer", {
      giftId: params.giftId,
      orderId: params.orderId,
      operation: params.operation,
    });
  },

  logGiftEligibleForSeller(params: {
    giftId: string;
    orderId?: string | undefined;
    operation: string;
  }) {
    log.debug("Gift linked to cancelled order where user is seller", {
      giftId: params.giftId,
      orderId: params.orderId,
      operation: params.operation,
    });
  },

  logQuerySuccess(params: {
    tg_id: string;
    totalGifts: number;
    availableGifts: number;
    operation: string;
  }) {
    log.info("Successfully retrieved user gifts available for withdrawal", {
      operation: params.operation,
      tg_id: params.tg_id,
      totalGifts: params.totalGifts,
      availableGifts: params.availableGifts,
    });
  },

  logQueryError(error: unknown, params: { tg_id: string; operation: string }) {
    log.error("Failed to get user gifts available for withdrawal", error, {
      operation: params.operation,
      tg_id: params.tg_id,
    });
  },
};
