import { loadEnvironment } from "../../config/env-loader";
import { DBGiftsCollection } from "../../firebase/firebase-admin";
import type { AppDate, GiftEntity, OrderGift } from "../../marketplace-shared";
import { GiftStatus } from "../../marketplace-shared";
import { GiftServiceLogger } from "./gift.logger";
import { validateGiftData } from "./gift.validator";
import { validateTelegramId } from "../user-service/user.validator";

loadEnvironment();

export type GiftFromBot = Omit<
  GiftEntity,
  "id" | "createdAt" | "updatedAt" | "collectionId" | "owner_tg_id"
>;

export async function createGift(
  giftData: Omit<GiftEntity, "id" | "createdAt" | "updatedAt">,
  batch?: FirebaseFirestore.WriteBatch
) {
  try {
    const giftRef = DBGiftsCollection.doc();

    const giftEntity: GiftEntity = {
      id: giftRef.id,
      ...giftData,
      createdAt: new Date() as AppDate,
      updatedAt: new Date() as AppDate,
    };

    if (batch) {
      batch.set(giftRef, giftEntity);
    } else {
      await giftRef.set(giftEntity);
    }

    GiftServiceLogger.logGiftCreated({
      giftId: giftRef.id,
      ownerTgId: giftData.owner_tg_id,
      collectionId: giftData.collectionId,
    });

    return giftRef.id;
  } catch (error) {
    GiftServiceLogger.logGiftCreationError({
      error,
      ownerTgId: giftData.owner_tg_id,
      collectionId: giftData.collectionId,
    });
    throw new Error(`Failed to create gift: ${error}`);
  }
}

export async function createGiftDirectly(params: {
  gift: GiftFromBot | OrderGift;
  userTgId: string;
  collectionId?: string;
}) {
  const { gift, userTgId, collectionId } = params;

  // Validate input parameters
  validateTelegramId(userTgId);

  const giftValidation = validateGiftData(gift);
  if (!giftValidation.isValid) {
    throw new Error(giftValidation.error);
  }

  GiftServiceLogger.logDirectGiftDepositStarted({
    userTgId,
    collectionId: collectionId || "",
    giftModel: gift.model.name,
  });

  try {
    const giftData = {
      owner_tg_id: userTgId,
      collectionId: collectionId || "",
      status: GiftStatus.DEPOSITED,
      base_name: gift.base_name,
      owned_gift_id: gift.owned_gift_id,
      model: gift.model,
      symbol: gift.symbol,
      backdrop: gift.backdrop,
    };

    const giftId = await createGift(giftData);

    GiftServiceLogger.logDirectGiftDepositSuccess({
      giftId,
      userTgId,
      collectionId: collectionId || "",
    });

    return {
      success: true,
      message: "Gift deposited successfully.",
      giftId,
    };
  } catch (error) {
    GiftServiceLogger.logDirectGiftDepositError({
      error,
      userTgId,
      collectionId: collectionId || "",
    });
    throw error;
  }
}

export async function updateGiftOwnership(
  giftId: string,
  newOwnerTgId: string,
  status: GiftStatus,
  batch?: FirebaseFirestore.WriteBatch
) {
  try {
    const giftRef = DBGiftsCollection.doc(giftId);

    const updateData = {
      owner_tg_id: newOwnerTgId,
      status,
      updatedAt: new Date() as AppDate,
    };

    if (batch) {
      batch.update(giftRef, updateData);
    } else {
      await giftRef.update(updateData);
    }

    GiftServiceLogger.logGiftOwnershipUpdated({
      giftId,
      newOwnerTgId,
      status,
    });
  } catch (error) {
    GiftServiceLogger.logGiftOwnershipUpdateError({
      error,
      giftId,
      newOwnerTgId,
      status,
    });
    throw new Error(`Failed to update gift ownership: ${error}`);
  }
}
