import {
  DBGiftsCollection,
  DBOrdersCollection,
} from "../../firebase/firebase-admin";
import {
  GiftEntity,
  GiftStatus,
  OrderEntity,
  OrderStatus,
} from "../../marketplace-shared";
import {
  GIFT_WITHDRAWAL_ERRORS,
  VALIDATION_ERRORS,
} from "../shared/error.constants";

export function validateGiftData(gift: any) {
  if (!gift) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.REQUIRED_FIELD_MISSING,
    };
  }

  if (!gift.model || !gift.symbol || !gift.backdrop) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.GIFT_MISSING_REQUIRED_DATA,
    };
  }

  return { isValid: true };
}

export async function validateGiftExists(giftId: string) {
  try {
    const giftDoc = await DBGiftsCollection.doc(giftId).get();

    if (!giftDoc.exists) {
      return {
        isValid: false,
        error: GIFT_WITHDRAWAL_ERRORS.GIFT_NOT_FOUND,
      };
    }

    const gift = { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
    return {
      isValid: true,
      gift,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Failed to validate gift existence: ${error}`,
    };
  }
}

export function validateGiftOwnership(gift: GiftEntity, userTgId: string) {
  if (gift.owner_tg_id !== userTgId) {
    return {
      isValid: false,
      error: GIFT_WITHDRAWAL_ERRORS.GIFT_NOT_OWNED,
    };
  }

  return { isValid: true };
}

export function validateGiftStatus(
  gift: GiftEntity,
  expectedStatus: GiftStatus
) {
  if (gift.status !== expectedStatus) {
    return {
      isValid: false,
      error: GIFT_WITHDRAWAL_ERRORS.GIFT_NOT_DEPOSITED,
    };
  }

  return { isValid: true };
}

export async function validateGiftWithdrawalEligibility(
  giftId: string,
  userId: string
) {
  try {
    const ordersQuery = await DBOrdersCollection.where(
      "giftId",
      "==",
      giftId
    ).get();

    if (ordersQuery.empty) {
      // Gift not linked to any order - eligible for withdrawal with fee
      return { isValid: true };
    }

    const orders = ordersQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    // Check for exclusionary conditions (active orders that prevent withdrawal)
    const hasActiveOrder = orders.some(
      (order) =>
        [
          OrderStatus.ACTIVE,
          OrderStatus.PAID,
          OrderStatus.CREATED,
          OrderStatus.GIFT_SENT_TO_RELAYER,
        ].includes(order.status) && order.sellerId === userId
    );

    if (hasActiveOrder) {
      return {
        isValid: false,
        error: GIFT_WITHDRAWAL_ERRORS.ACTIVE_ORDER_EXISTS,
      };
    }

    // Check for withdrawal-eligible conditions
    for (const order of orders) {
      // Case 1: Order with status "gift_sent_to_relayer" and user is buyer
      if (
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
        order.buyerId === userId
      ) {
        return { isValid: true };
      }

      // Case 2: Order with status "cancelled" and user is seller
      if (order.status === OrderStatus.CANCELLED && order.sellerId === userId) {
        return { isValid: true };
      }
    }

    return {
      isValid: false,
      error: GIFT_WITHDRAWAL_ERRORS.NOT_ELIGIBLE,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Failed to validate gift withdrawal eligibility: ${error}`,
    };
  }
}

export function validateGiftForWithdrawal(gift: GiftEntity, userTgId: string) {
  // Validate ownership
  const ownershipValidation = validateGiftOwnership(gift, userTgId);
  if (!ownershipValidation.isValid) {
    return ownershipValidation;
  }

  // Validate status
  const statusValidation = validateGiftStatus(gift, GiftStatus.DEPOSITED);
  if (!statusValidation.isValid) {
    return statusValidation;
  }

  return { isValid: true };
}
