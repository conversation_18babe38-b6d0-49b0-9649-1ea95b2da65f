import { DBUserCollection } from "../../firebase/firebase-admin";
import { UserEntity } from "../../marketplace-shared";
import {
  USER_BALANCE_ERRORS,
  VALIDATION_ERRORS,
} from "../shared/error.constants";

export function validateUserId(userId?: string) {
  if (!userId) {
    throw new Error(VALIDATION_ERRORS.INVALID_USER_ID);
  }
}

export function validateTelegramId(tgId?: string) {
  if (!tgId) {
    throw new Error(VALIDATION_ERRORS.INVALID_TELEGRAM_ID);
  }
}

export function validateAmount(amount: number) {
  if (amount <= 0) {
    return {
      isValid: false,
      error: USER_BALANCE_ERRORS.INVALID_AMOUNT,
    };
  }

  return { isValid: true };
}

export async function validateUserExists(userId: string) {
  try {
    const userDoc = await DBUserCollection.doc(userId).get();

    if (!userDoc.exists) {
      return {
        isValid: false,
        error: USER_BALANCE_ERRORS.USER_NOT_FOUND,
      };
    }

    const user = { id: userDoc.id, ...userDoc.data() } as UserEntity;
    return {
      isValid: true,
      user,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Failed to validate user existence: ${error}`,
    };
  }
}

export function validateSufficientBalance(
  userBalance: { sum: number; locked: number },
  requiredAmount: number
) {
  const availableBalance = userBalance.sum - userBalance.locked;

  if (availableBalance < requiredAmount) {
    return {
      hasSufficientBalance: false,
      requiredAmount,
      availableBalance,
      error: `${USER_BALANCE_ERRORS.INSUFFICIENT_FUNDS} ${requiredAmount} TON. Available: ${availableBalance} TON`,
    };
  }

  return {
    hasSufficientBalance: true,
    requiredAmount,
    availableBalance,
  };
}

export async function validateUserBalanceForSpending(
  userId: string,
  amount: number
) {
  try {
    // Validate amount
    const amountValidation = validateAmount(amount);
    if (!amountValidation.isValid) {
      return {
        hasSufficientBalance: false,
        requiredAmount: amount,
        availableBalance: 0,
        error: amountValidation.error || "Amount validation failed",
      };
    }

    // Validate user exists
    const userValidation = await validateUserExists(userId);
    if (!userValidation.isValid || !userValidation.user) {
      return {
        hasSufficientBalance: false,
        requiredAmount: amount,
        availableBalance: 0,
        error: userValidation.error || "User validation failed",
      };
    }

    const user = userValidation.user;
    const userBalance = user.balance || { sum: 0, locked: 0 };

    // Validate sufficient balance
    return validateSufficientBalance(userBalance, amount);
  } catch (error) {
    return {
      hasSufficientBalance: false,
      requiredAmount: amount,
      availableBalance: 0,
      error: `Failed to validate user balance: ${error}`,
    };
  }
}

export function validateUserData(userData: any) {
  if (!userData) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.REQUIRED_FIELD_MISSING,
    };
  }

  if (!userData.tg_id) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.INVALID_TELEGRAM_ID,
    };
  }

  return { isValid: true };
}
