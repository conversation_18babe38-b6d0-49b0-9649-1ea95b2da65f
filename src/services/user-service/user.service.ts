import { DBUserCollection } from "../../firebase/firebase-admin";
import { UserEntity } from "../../marketplace-shared";
import { getUserSession } from "../session-service/session-service";
import { VALIDATION_ERRORS } from "../shared/error.constants";

export async function getUserById(userId: string) {
  try {
    const userDoc = await DBUserCollection.doc(userId).get();

    if (!userDoc.exists) {
      return null;
    }

    return { id: userDoc.id, ...userDoc.data() } as UserEntity;
  } catch (error) {
    throw new Error(`Failed to get user by ID: ${error}`);
  }
}

export async function findUserIdByTgId(tgId: string) {
  try {
    const usersQuery = await DBUserCollection.where("tg_id", "==", tgId)
      .limit(1)
      .get();

    if (usersQuery.empty) {
      return {
        success: false,
        message: "User not found with the provided Telegram ID.",
      };
    }

    const userDoc = usersQuery.docs[0];
    if (!userDoc) {
      return {
        success: false,
        message: "User document not found.",
      };
    }

    return {
      success: true,
      userId: userDoc.id,
    };
  } catch (error) {
    throw new Error(`Failed to find user by Telegram ID: ${error}`);
  }
}

export async function resolveUserId(params: {
  userId?: string;
  tgId?: string;
}) {
  const { userId, tgId } = params;

  if (userId) {
    return {
      success: true,
      userId,
    };
  }

  if (tgId) {
    return await findUserIdByTgId(tgId);
  }

  return {
    success: false,
    message: "Either userId or tgId is required.",
  };
}

export async function getUserDataWithSession(userId: string) {
  const user = await getUserById(userId);
  if (!user) {
    throw new Error(VALIDATION_ERRORS.USER_NOT_FOUND);
  }

  const session = await getUserSession(userId);

  return {
    user,
    session,
    tgId: user.tg_id as string,
    language: session?.language_preference || "en",
  };
}

export async function getUserTgIdByUserId(userId: string) {
  const user = await getUserById(userId);
  if (!user?.tg_id) {
    throw new Error(VALIDATION_ERRORS.USER_NOT_FOUND);
  }
  return user.tg_id;
}
