import { BOT_TOKEN } from "../../app.constants";
import { loadEnvironment } from "../../config/env-loader";
import {
  clearUserSessionByBot,
  getUserSessionByBot,
  saveUserSessionByBot,
} from "../bot-session-service/bot-session-service";
import { BotSessionEntity } from "../../marketplace-shared";
import { SessionServiceLogger } from "./session-service.logger";

loadEnvironment();

export const setUserSession = async (
  userId: string,
  session: Partial<BotSessionEntity>
) => {
  try {
    const result = await saveUserSessionByBot({
      userId,
      botToken: BOT_TOKEN as string,
      sessionData: session,
    });

    if (!result.success) {
      throw new Error(result.message ?? "Failed to save session");
    }
  } catch (error) {
    SessionServiceLogger.logSetSessionError({ error, userId });
    throw error;
  }
};

export const getUserSession = async (userId: string) => {
  try {
    const result = await getUserSessionByBot({
      userId,
    });

    if (!result.success) {
      throw new Error(result.message ?? "Failed to get session");
    }

    return result.session ?? undefined;
  } catch (error) {
    SessionServiceLogger.logGetSessionError({ error, userId });
    return undefined;
  }
};

export const clearUserSession = async (userId: string) => {
  try {
    const result = await clearUserSessionByBot({
      userId,
    });

    if (!result.success) {
      throw new Error(result.message ?? "Failed to clear session");
    }
  } catch (error) {
    SessionServiceLogger.logClearSessionError({ error, userId });
    throw error;
  }
};

export const updateUserSession = async (
  userId: string,
  updates: Partial<BotSessionEntity>
) => {
  try {
    const currentSession = (await getUserSession(userId)) ?? {};
    const updatedSession = { ...currentSession, ...updates };
    await setUserSession(userId, updatedSession as BotSessionEntity);
  } catch (error) {
    SessionServiceLogger.logUpdateSessionError({ error, userId });
    throw error;
  }
};

export const clearSessionProperty = async (
  userId: string,
  property: keyof BotSessionEntity
) => {
  try {
    const session = await getUserSession(userId);
    if (session) {
      delete session[property];
      if (Object.keys(session).length === 0) {
        await clearUserSession(userId);
      } else {
        await setUserSession(userId, session);
      }
    }
  } catch (error) {
    SessionServiceLogger.logClearSessionPropertyError({
      error,
      userId,
      property,
    });
    throw error;
  }
};
