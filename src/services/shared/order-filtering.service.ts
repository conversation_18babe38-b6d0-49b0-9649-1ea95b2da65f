import { OrderEntity, OrderStatus } from "../../marketplace-shared";

const ACTIVE_ORDER_STATUSES = [
  OrderStatus.ACTIVE,
  OrderStatus.PAID,
  OrderStatus.CREATED,
];

const WITHDRAWABLE_STATUSES = [
  OrderStatus.GIFT_SENT_TO_RELAYER,
  OrderStatus.CANCELLED,
];

export async function analyzeOrdersForGift(
  orders: OrderEntity[],
  giftId: string,
  userId: string
) {
  const relatedOrders = orders.filter((order) =>
    order.gift_id_list?.includes(giftId)
  );

  if (relatedOrders.length === 0) {
    return {
      hasActiveOrder: false,
      hasWithdrawableOrder: true,
      relatedOrder: null,
    };
  }

  // Check for active orders (seller only)
  const hasActiveOrder = relatedOrders.some(
    (order) =>
      ACTIVE_ORDER_STATUSES.includes(order.status) && order.sellerId === userId
  );

  if (hasActiveOrder) {
    return {
      hasActiveOrder: true,
      hasWithdrawableOrder: false,
      relatedOrder: null,
    };
  }

  // Find withdrawable order
  const withdrawableOrder = relatedOrders.find((order) => {
    if (!WITHDRAWABLE_STATUSES.includes(order.status)) return false;

    return (
      (order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
        order.buyerId === userId) ||
      (order.status === OrderStatus.CANCELLED && order.sellerId === userId)
    );
  });

  if (withdrawableOrder) {
    return {
      hasActiveOrder: false,
      hasWithdrawableOrder: true,
      relatedOrder: {
        id: withdrawableOrder.id,
        number: withdrawableOrder.number,
        status: withdrawableOrder.status,
        price: withdrawableOrder.price,
      },
    };
  }

  return {
    hasActiveOrder: false,
    hasWithdrawableOrder: false,
    relatedOrder: null,
  };
}
