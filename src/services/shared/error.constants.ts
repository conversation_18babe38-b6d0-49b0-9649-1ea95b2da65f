export const VALIDATION_ERRORS = {
  // User validation errors
  USER_NOT_FOUND: 'User not found',
  INVALID_USER_ID: 'User ID is required',
  INVALID_TELEGRAM_ID: 'Telegram ID is required',
  
  // Gift validation errors
  GIFT_NOT_FOUND: 'Gift not found',
  INVALID_GIFT_ID: 'Gift ID is required',
  GIFT_NOT_OWNED_BY_USER: 'Gift does not belong to this user',
  GIFT_NOT_AVAILABLE_FOR_WITHDRAWAL: 'Gift is not available for withdrawal',
  GIFT_LINKED_TO_ACTIVE_ORDER: 'Gift is currently linked to an active order and cannot be withdrawn',
  GIFT_NOT_ELIGIBLE_FOR_WITHDRAWAL: 'Gift is not eligible for withdrawal under current conditions',
  GIFT_MISSING_REQUIRED_DATA: 'Gift must have model, symbol, and backdrop',
  
  // Order validation errors
  ORDER_NOT_FOUND: 'Order not found',
  INVALID_ORDER_ID: 'Order ID is required',
  INVALID_ORDER_STATUS: 'Order status does not allow this operation',
  
  // Balance validation errors
  INSUFFICIENT_BALANCE: 'Insufficient available balance',
  INVALID_AMOUNT: 'Amount must be positive',
  
  // General validation errors
  REQUIRED_FIELD_MISSING: 'Required field is missing',
  INVALID_INPUT: 'Invalid input provided',
} as const;

export const GIFT_WITHDRAWAL_ERRORS = {
  GIFT_NOT_FOUND: 'Gift not found',
  GIFT_NOT_OWNED: 'Gift does not belong to this user',
  GIFT_NOT_DEPOSITED: 'Gift is not available for withdrawal',
  USER_NOT_FOUND: 'User not found',
  ACTIVE_ORDER_EXISTS: 'Gift is currently linked to an active order and cannot be withdrawn',
  NOT_ELIGIBLE: 'Gift is not eligible for withdrawal under current conditions',
} as const;

export const USER_BALANCE_ERRORS = {
  USER_NOT_FOUND: 'User not found',
  INSUFFICIENT_FUNDS: 'Insufficient available balance to spend',
  INVALID_AMOUNT: 'Amount must be positive',
} as const;
