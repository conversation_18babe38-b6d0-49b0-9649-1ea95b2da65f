{"businessConnection.giftReadyForBuyer": "🎁 Great news! Your gift for order #{orderNumber} is ready for delivery. Please check your orders.", "businessConnection.giftSentError": "❌ Failed to send gift to relayer. Please try again.", "businessConnection.giftSentSuccess": "✅ Gift successfully sent to relayer! The buyer will be notified.", "businessConnection.giftTransferGenericError": "❌ Failed to transfer gift. Please try again.", "businessConnection.giftTransferredSuccess": "✅ Gift successfully transferred!", "businessConnection.incorrectGift": "❌ This gift does not match the order requirements. Please send the correct gift.", "businessConnection.noGiftToTransfer": "❌ No gift found to transfer.", "businessConnection.orderNotFound": "❌ Order not found. Please check the order ID and try again.", "businessConnection.processingGift": "⏳ Processing your gift...", "businessConnection.processingWithdrawal": "⏳ Processing your withdrawal...", "businessConnection.withdrawalError": "❌ Failed to withdraw gift. Please try again.", "businessConnection.withdrawalSuccess": "✅ Gift successfully withdrawn!", "buttons.cancel": "❌ Cancel", "buttons.contactSupport": "📞 Contact Support", "buttons.depositGift": "📦 Deposit Gift", "buttons.depositGiftInstructions": "🎁 Deposit a Gift\n\nYou should go to {premRelayerUsername} and just deposit a gift. Then, you will see your deposited gift in the Pram app in the tab called 'My Gifts.'\n\nSteps:\n1. Go to {premRelayerUsername}\n2. Send your gift to the relayer\n3. Your gift will appear in the Pram app under 'My Gifts'\n\nThis allows you to deposit gifts without linking them to a specific order.", "buttons.myGifts": "🎁 My Gifts", "buttons.openMarketplace": "🌐 Open Marketplace", "buttons.openMarketplaceButton": "🌐 Open Marketplace", "buttons.orderHelpButton": "📋 Order Help", "buttons.withdrawGift": "Withdraw {giftName}", "callbacks.backToMenu": "🏠 Back to Main Menu", "callbacks.contactSupport": "📞 Contact Support\n\nFor any questions or issues, please reach out to {PREM_SUPPORT_OFFICIAL}", "callbacks.openingMarketplace": "🌐 Opening Marketplace...", "callbacks.orderHelp": "❓ Order Help\n\nIf you need assistance with your orders, please contact our support team.", "commands.health.description": "Check bot health status", "commands.health.error": "❌ Error checking health status", "commands.health.lastCheck": "{status}\n\nLast healthcheck: {timestamp}", "commands.health.noData": "❌ No healthcheck data found", "commands.health.statusHealthy": "✅ Healthy", "commands.health.statusUnhealthy": "⚠️ Unhealthy", "commands.help.description": "Show help information", "commands.start.description": "Start the bot and show main menu", "common.botDescription": "Welcome to {APP_NAME}🎁 – the first liquid pre-market for Telegram unupgraded gifts!\nWith {APP_NAME}, you can:\n\nAs buyer:\n\n🔓 Buy any unupgraded TG gift.\n💸 Resell for instant profit\n\nAs seller:\n\n🎁 Sell unupgraded TG gift with just 50% collateral.\n💰 Earn fees from resales\n\nEnjoy fast, safe, and easy gift trading!", "common.botGenericError": "Sorry, something went wrong. Please try again later.", "common.botShortDescription": "🎁 {APP_NAME} - Telegram Gifts Marketplace", "common.genericError": "❌ Failed to process your request. Please try again later.", "common.giftSelectedForWithdrawal": "Gift selected for withdrawal", "common.giftWithdrawalInstructions": "✅ Gift selected for withdrawal! Now go to @{relayerUsername} and send the command 'get a gift' to complete the withdrawal.", "common.giftWithdrawalSimulatedSuccessfully": "Gift withdrawal simulated successfully", "common.giftWithdrawalSimulationFailed": "❌ Simulation Mode: Gift withdrawal failed", "common.giftWithdrawalSimulationFailedCallback": "Gift withdrawal simulation failed", "common.giftWithdrawalSimulationSuccess": "🎉 Simulation Mode: Gift withdrawn successfully!", "common.help": "Welcome to {APP_NAME}! \n \n{PREM_CHANNEL} - Community \n{PREM_SUPPORT_OFFICIAL} - Support \n{relayerUsername} - Gift Relayer", "common.telegramIdError": "❌ Unable to identify your Telegram ID. Please try again.", "common.welcome": "🛍️ Welcome to the {APP_NAME} Bot!", "common.withdrawalErrorFallback": "Failed to withdraw gift", "gifts.depositSuccess": "🎁 Gift deposited successfully! You can now see your gift in the {APP_NAME} app under 'My Gifts' tab.", "gifts.fetchError": "❌ Error fetching your gifts. Please try again later.", "gifts.fetchingGifts": "🔄 Fetching your gifts...", "gifts.giftsAvailableForWithdrawal": "🎁 Your Gifts Available for <PERSON><PERSON><PERSON> ({count})", "gifts.linkedOrder": "\n📦 Order #{orderNumber} ({orderStatus})", "gifts.noGiftsAvailable": "📭 No gifts available. Send gifts to {relayerUsername} to get started!", "gifts.noLinkedOrder": "\n📦 No linked order", "gifts.unknownGift": "Unknown Gift", "gifts.withdrawInstructions": "Use the buttons below to withdraw specific gifts.", "language.detectionPromptRussian": "🌍 We detected that you are using the app in Russian language. Would you like to switch to Russian?", "language.detectionPromptUkrainian": "🌍 We detected that you are using the app in Ukrainian language. Would you like to switch to Ukrainian?", "language.keepEnglish": "No, I want to stay in English", "language.setToEnglish": "✅ Language set to English. Welcome to {APP_NAME} Bot!", "language.setToRussian": "✅ Язык изменен на русский. Добро пожаловать в {APP_NAME} Bot!", "language.setToUkrainian": "✅ Мову змінено на українську. Ласкаво просимо до {APP_NAME} Bot!", "language.switchToRussian": "Yes, let's switch to Russian", "language.switchToUkrainian": "Yes, let's switch to Ukrainian", "notifications.buyerGiftSent": "🎁 Great news! The gift for your order #{orderNumber} has been sent to the bot. You can now claim it from @{relayerUsername}.", "notifications.buyerGiftSentWithLink": "🎁 Great news! The gift for your order #{orderNumber} has been sent to the bot. You can now claim it from @{relayerUsername}.\n\n📱 View order details: {orderLink}", "notifications.newProposalReceived": "💰 New price proposal received for your order #{orderNumber}! Someone proposed {proposedPrice} TON (original price: {originalPrice} TON).\n\n📱 View order details: {orderLink}", "notifications.proposalAccepted": "✅ Great news! Your price proposal of {proposedPrice} TON for order #{orderNumber} has been accepted by the seller.\n\n📱 View order details: {orderLink}", "notifications.sellerOrderPaid": "🎉 Congratulations! Your order #{orderNumber} has been purchased for {price} TON. You can now send the gift to complete the order.", "notifications.sellerOrderPaidWithLink": "🎉 Congratulations! Your order #{orderNumber} has been purchased for {price} TON. You can now send the gift to complete the order.\n\n📱 View order details: {orderLink}", "simulation.giftDepositError": "❌ Failed to deposit mock gift: {errorMessage}\n\nPlease try again or contact support if the issue persists.", "simulation.giftDepositMode": "🎁 Deposit a Gift (Simulation Mode)\n\n🔧 SIMULATION MODE: Generating and depositing a mock gift...", "simulation.giftDepositSuccess": "🎁 Gift Details:\n• Name: {giftName}\n• Model: {modelName}\n• Symbol: {symbolName}\n• Backdrop: {backdropName}\n\nYour gift is now available in the {APP_NAME} app under 'My Gifts' tab.", "simulation.giftWithdrawalError": "❌ Gift withdrawal failed in simulation mode: {errorMessage}\n\nPlease try again or contact support if the issue persists.", "simulation.giftWithdrawalMode": "🎁 Gift Withdrawal (Simulation Mode)\n\n🔧 SIMULATION MODE: Processing gift withdrawal...", "simulation.giftWithdrawalSuccess": "✅ Gift withdrawal completed successfully in simulation mode!\n\n🔧 SIMULATION MODE: Gift transfer skipped. In real mode, your gift would be transferred to you now.", "simulation.orderActivation": "\n\n🔧 DEV MODE: This is a simulation. In production, you would need to deposit a gift first.", "simulation.orderView": "🔧 DEV MODE: This order is in simulation mode.", "simulation.sellerGiftDeposit": "\n\n🔧 DEV MODE: This is a simulation. In production, you would send the gift to @premrelayer.", "support.contactInfo": "📞 For support, please contact {PREM_SUPPORT_OFFICIAL}"}