@import 'tailwindcss';
@import 'tw-animate-css';
@import '@telegram-apps/telegram-ui/dist/styles.css';
@import '../styles/theme.css';

@custom-variant dark (&:is(.dark *));
@custom-variant black (&:is(.black *));

@theme inline {
  --breakpoint-xss: 375px;
  --breakpoint-xs: 475px;

  /* Semantic Colors */
  --color-background: var(--color-background);
  --color-foreground: var(--color-foreground);
  --color-primary: var(--color-primary);
  --color-primary-foreground: var(--color-primary-foreground);
  --color-secondary: var(--color-secondary);
  --color-secondary-foreground: var(--color-secondary-foreground);
  --color-accent: var(--color-accent);
  --color-accent-foreground: var(--color-accent-foreground);
  --color-destructive: var(--color-destructive);
  --color-muted: var(--color-muted);
  --color-muted-foreground: var(--color-muted-foreground);
  --color-card: var(--color-card);
  --color-card-foreground: var(--color-card-foreground);
  --color-popover: var(--color-popover);
  --color-popover-foreground: var(--color-popover-foreground);
  --color-border: var(--color-border);
  --color-input: var(--color-input);
  --color-ring: var(--color-ring);

  /* Chart Colors */
  --color-chart-1: var(--color-chart-1);
  --color-chart-2: var(--color-chart-2);
  --color-chart-3: var(--color-chart-3);
  --color-chart-4: var(--color-chart-4);
  --color-chart-5: var(--color-chart-5);

  /* Sidebar Colors */
  --color-sidebar: var(--color-sidebar);
  --color-sidebar-foreground: var(--color-sidebar-foreground);
  --color-sidebar-primary: var(--color-sidebar-primary);
  --color-sidebar-primary-foreground: var(--color-sidebar-primary-foreground);
  --color-sidebar-accent: var(--color-sidebar-accent);
  --color-sidebar-accent-foreground: var(--color-sidebar-accent-foreground);
  --color-sidebar-border: var(--color-sidebar-border);
  --color-sidebar-ring: var(--color-sidebar-ring);

  /* Brand Colors */
  --color-ton-main: var(--color-ton-main);
  --color-ton-gray: var(--color-ton-gray);
  --color-ton-black: var(--color-ton-black);
  --color-telegram-blue: var(--color-telegram-blue);
  --color-telegram-light-blue: var(--color-telegram-light-blue);
  --color-telegram-dark-bg: var(--color-telegram-dark-bg);
  --color-telegram-secondary-bg: var(--color-telegram-secondary-bg);
  --color-telegram-text: var(--color-telegram-text);
  --color-telegram-hint: var(--color-telegram-hint);

  /* Fonts */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Tailwind Color Utilities */
  --color-red-600: var(--color-red-600);
  --color-green-600: var(--color-green-600);
  --color-blue-500: var(--color-blue-500);
  --color-yellow-500: var(--color-yellow-500);
  --color-purple-500: var(--color-purple-500);
  --color-pink-500: var(--color-pink-500);
  --color-orange-500: var(--color-orange-500);
  --color-red-400: var(--color-red-400);
  --color-green-400: var(--color-green-400);
  --color-blue-400: var(--color-blue-400);
  --color-yellow-400: var(--color-yellow-400);
  --color-purple-400: var(--color-purple-400);
  --color-orange-400: var(--color-orange-400);

  /* Status Colors with Opacity */
  --color-yellow-500-10: color-mix(in srgb, var(--color-yellow-500) 10%, transparent);
  --color-yellow-500-20: color-mix(in srgb, var(--color-yellow-500) 20%, transparent);
  --color-blue-500-10: color-mix(in srgb, var(--color-blue-500) 10%, transparent);
  --color-blue-500-20: color-mix(in srgb, var(--color-blue-500) 20%, transparent);
  --color-green-500-10: color-mix(in srgb, var(--color-green-500) 10%, transparent);
  --color-green-500-20: color-mix(in srgb, var(--color-green-500) 20%, transparent);
  --color-purple-500-10: color-mix(in srgb, var(--color-purple-500) 10%, transparent);
  --color-purple-500-20: color-mix(in srgb, var(--color-purple-500) 20%, transparent);
  --color-pink-500-10: color-mix(in srgb, var(--color-pink-500) 10%, transparent);
  --color-orange-500-10: color-mix(in srgb, var(--color-orange-500) 10%, transparent);
  --color-orange-500-20: color-mix(in srgb, var(--color-orange-500) 20%, transparent);
}

:root {
  --radius: 0.625rem;

  /* Semantic color mappings for shadcn/ui compatibility */
  --background: var(--color-background);
  --foreground: var(--color-foreground);
  --card: var(--color-card);
  --card-foreground: var(--color-card-foreground);
  --popover: var(--color-popover);
  --popover-foreground: var(--color-popover-foreground);
  --primary: var(--color-primary);
  --primary-foreground: var(--color-primary-foreground);
  --secondary: var(--color-secondary);
  --secondary-foreground: var(--color-secondary-foreground);
  --muted: var(--color-muted);
  --muted-foreground: var(--color-muted-foreground);
  --accent: var(--color-accent);
  --accent-foreground: var(--color-accent-foreground);
  --destructive: var(--color-destructive);
  --border: var(--color-border);
  --input: var(--color-input);
  --ring: var(--color-ring);
  --chart-1: var(--color-chart-1);
  --chart-2: var(--color-chart-2);
  --chart-3: var(--color-chart-3);
  --chart-4: var(--color-chart-4);
  --chart-5: var(--color-chart-5);
  --sidebar: var(--color-sidebar);
  --sidebar-foreground: var(--color-sidebar-foreground);
  --sidebar-primary: var(--color-sidebar-primary);
  --sidebar-primary-foreground: var(--color-sidebar-primary-foreground);
  --sidebar-accent: var(--color-sidebar-accent);
  --sidebar-accent-foreground: var(--color-sidebar-accent-foreground);
  --sidebar-border: var(--color-sidebar-border);
  --sidebar-ring: var(--color-sidebar-ring);

  /* Telegram theme mappings */
  --tg-theme-bg-color: var(--color-tg-bg);
  --tg-theme-text-color: var(--color-tg-text);
  --tg-theme-hint-color: var(--color-tg-hint);
  --tg-theme-link-color: var(--color-tg-link);
  --tg-theme-button-color: var(--color-tg-button);
  --tg-theme-button-text-color: var(--color-tg-button-text);
  --tg-theme-secondary-bg-color: var(--color-tg-secondary-bg);
  --tg-theme-header-bg-color: var(--color-tg-header-bg);
  --tg-theme-bottom-bar-bg-color: var(--color-tg-bottom-bar-bg);
  --tg-theme-accent-text-color: var(--color-tg-accent-text);
  --tg-theme-section-bg-color: var(--color-tg-section-bg);
  --tg-theme-section-header-text-color: var(--color-tg-section-header-text);
  --tg-theme-section-separator-color: var(--color-tg-section-separator);
  --tg-theme-subtitle-text-color: var(--color-tg-subtitle-text);
  --tg-theme-destructive-text-color: var(--color-tg-destructive-text);
}

/* Dark theme overrides are now handled in theme.css */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  body[data-scroll-locked] {
    margin-right: 0 !important;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: var(--tg-theme-hint-color)
      var(--tg-theme-secondary-bg-color);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: var(--tg-theme-secondary-bg-color);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: var(--tg-theme-hint-color);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: var(--tg-theme-accent-text-color);
  }

  [data-radix-popper-content-wrapper] {
    z-index: 9999 !important;
  }
}

@media (max-width: 768px) {
  [data-vaul-drawer] {
    touch-action: none;
  }

  [data-vaul-drawer][data-vaul-drawer-direction='bottom'] {
    transform: translate3d(0, 100%, 0);
  }

  [data-vaul-drawer][data-vaul-drawer-direction='bottom'][data-state='open'] {
    transform: translate3d(0, 0, 0);
  }

  [data-vaul-drawer-wrapper] {
    height: 100vh;
    height: 100dvh;
  }

  body:has([data-vaul-drawer][data-state='open']) {
    overflow: hidden;
  }
}

/* Collapsible animations */
@keyframes collapsible-down {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
  }
}

@keyframes collapsible-up {
  from {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
  }
  to {
    height: 0;
    opacity: 0;
  }
}

.animate-collapsible-down {
  animation: collapsible-down 300ms ease-in-out;
}

.animate-collapsible-up {
  animation: collapsible-up 300ms ease-in-out;
}
