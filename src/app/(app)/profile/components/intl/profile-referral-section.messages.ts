import { defineMessages } from 'react-intl';

export const profileReferralSectionMessages = defineMessages({
  referralProgram: {
    id: 'profile.referralSection.referralProgram',
    defaultMessage: 'Referral Program',
  },
  loadingReferralData: {
    id: 'profile.referralSection.loadingReferralData',
    defaultMessage: 'Loading referral data...',
  },
  failedToLoadReferrals: {
    id: 'profile.referralSection.failedToLoadReferrals',
    defaultMessage: 'Failed to load referrals',
  },
  yourReferralRate: {
    id: 'profile.referralSection.yourReferralRate',
    defaultMessage: 'Your Referral Rate',
  },
  referralRateDescription: {
    id: 'profile.referralSection.referralRateDescription',
    defaultMessage:
      'You earn {percentage}% of the purchase fee when your referrals make purchases',
  },
  sharing: {
    id: 'profile.referralSection.sharing',
    defaultMessage: 'Sharing...',
  },
  shareReferralLink: {
    id: 'profile.referralSection.shareReferralLink',
    defaultMessage: 'Share Referral Link',
  },
  shareTheLinkGetPoints: {
    id: 'profile.referralSection.shareTheLinkGetPoints',
    defaultMessage: 'Share the link - get points for gifts!',
  },
  friends: {
    id: 'profile.referralSection.friends',
    defaultMessage: 'friends',
  },
  points: {
    id: 'profile.referralSection.points',
    defaultMessage: 'points',
  },
  yourReferrals: {
    id: 'profile.referralSection.yourReferrals',
    defaultMessage: 'Your Referrals ({count})',
  },
  name: {
    id: 'profile.referralSection.name',
    defaultMessage: 'Name',
  },
  potentialEarnings: {
    id: 'profile.referralSection.potentialEarnings',
    defaultMessage: 'Potential Earnings',
  },
  anonymous: {
    id: 'profile.referralSection.anonymous',
    defaultMessage: 'Anonymous',
  },
  ofTheirPurchaseFees: {
    id: 'profile.referralSection.ofTheirPurchaseFees',
    defaultMessage: 'of their purchase fees',
  },
  joinTheMarketplace: {
    id: 'profile.referralSection.joinTheMarketplace',
    defaultMessage: 'Join the Marketplace',
  },
  joinMeOnMarketplace: {
    id: 'profile.referralSection.joinMeOnMarketplace',
    defaultMessage:
      'Join me on this amazing marketplace and start earning rewards!',
  },
  referralLinkSharedSuccessfully: {
    id: 'profile.referralSection.referralLinkSharedSuccessfully',
    defaultMessage: 'Referral link shared successfully!',
  },
});
