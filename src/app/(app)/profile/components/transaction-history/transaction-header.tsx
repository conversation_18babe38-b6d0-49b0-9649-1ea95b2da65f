import { RefreshCw } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Badge } from '@/components/ui/badge';

import { transactionHeaderMessages } from './intl/transaction-header.messages';

interface TransactionHeaderProps {
  onRefresh: () => void;
  loading: boolean;
  refreshing: boolean;
}

export function TransactionHeader({
  onRefresh,
  loading,
  refreshing,
}: TransactionHeaderProps) {
  const { formatMessage: t } = useIntl();

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-center">
        <Badge
          variant="outline"
          className="bg-[var(--color-status-blue-bg)] border-[var(--color-status-blue-border)] text-[var(--color-status-blue)] text-xs font-medium"
        >
          {t(transactionHeaderMessages.beta)}
        </Badge>
      </div>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-foreground">
          {t(transactionHeaderMessages.transactionHistory)}
        </h3>
        <button
          onClick={onRefresh}
          disabled={loading || refreshing}
          className="flex items-center gap-2 px-3 py-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors disabled:opacity-50"
        >
          <RefreshCw
            className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`}
          />
          {t(transactionHeaderMessages.refresh)}
        </button>
      </div>
    </div>
  );
}
