import { ArrowDownRight, ArrowUpR<PERSON>, Clock } from 'lucide-react';
import { useIntl } from 'react-intl';

import {
  firebaseTimestampToDate,
  type UserTxEntity,
} from '@/mikerudenko/marketplace-shared';
import { getTransactionDescription } from '@/services/transaction-description-service';
import { useTransactionTypeText } from '@/services/transaction-type-service';
import {
  formatAmount,
  formatTransactionDate,
  getAmountColor,
} from '@/utils/transaction-utils';

interface MobileTransactionListProps {
  transactions: UserTxEntity[];
}

export function MobileTransactionList({
  transactions,
}: MobileTransactionListProps) {
  const intl = useIntl();
  const getTransactionTypeText = useTransactionTypeText();

  return (
    <div className="md:hidden space-y-3">
      {transactions.map((transaction) => (
        <div
          key={transaction.id}
          className="bg-card border border-border rounded-lg p-4 space-y-3 active:bg-input transition-colors"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-border/50">
                {transaction.amount >= 0 ? (
                  <ArrowDownRight className="w-4 h-4 text-[var(--color-status-green)]" />
                ) : (
                  <ArrowUpRight className="w-4 h-4 text-[var(--color-status-red)]" />
                )}
              </div>
              <div>
                <div className="text-foreground font-medium text-sm">
                  {getTransactionTypeText(transaction.tx_type)}
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="w-3 h-3" />
                  {formatTransactionDate(
                    firebaseTimestampToDate(transaction.createdAt),
                  )}
                </div>
              </div>
            </div>
            <div
              className={`font-semibold text-lg ${getAmountColor(transaction.amount)}`}
            >
              {formatAmount(transaction.amount)}
            </div>
          </div>

          <div className="text-muted-foreground text-sm break-words pl-11">
            {getTransactionDescription(transaction, intl)}
          </div>
        </div>
      ))}
    </div>
  );
}
