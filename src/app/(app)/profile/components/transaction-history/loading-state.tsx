import { Loader2 } from 'lucide-react';
import { useIntl } from 'react-intl';

import { loadingStateMessages } from './loading-state/intl/loading-state.messages';

interface LoadingStateProps {
  message?: string;
}

export function LoadingState({ message }: LoadingStateProps) {
  const { formatMessage: t } = useIntl();
  const defaultMessage =
    message || t(loadingStateMessages.loadingYourTransactions);

  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
      <p className="text-muted-foreground text-sm">{defaultMessage}</p>
    </div>
  );
}
