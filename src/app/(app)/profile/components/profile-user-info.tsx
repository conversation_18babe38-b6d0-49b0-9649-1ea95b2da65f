'use client';

import { Avatar } from '@telegram-apps/telegram-ui';
import { Gift, User } from 'lucide-react';
import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useRootContext } from '@/root-context';
import { getBalanceInfo } from '@/services/user-service';

import { profileUserInfoMessages } from './intl/profile-user-info.messages';

export const ProfileUserInfo = () => {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();

  if (!currentUser) {
    return null;
  }

  const { balance, availableBalance } = getBalanceInfo(currentUser);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t(profileUserInfoMessages.profileInformation)}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-card flex items-center justify-center">
            {currentUser.photoURL ? (
              <Avatar size={48} src={currentUser.photoURL} />
            ) : (
              <User className="w-8 h-8 text-muted-foreground" />
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold">
              {currentUser.displayName ||
                currentUser.name ||
                t(profileUserInfoMessages.anonymousUser)}
            </h3>
            {currentUser.tg_id && (
              <p className="text-xs text-muted-foreground">
                ID: {currentUser.tg_id}
              </p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-border">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              {t(profileUserInfoMessages.availableBalance)}
            </p>
            <p className="text-xl font-bold text-[var(--color-ton-main)] flex justify-center">
              {availableBalance.toFixed(2)} <TonLogo size={24} />
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              {t(profileUserInfoMessages.lockedBalance)}
            </p>
            <p className="text-xl font-bold text-[var(--color-status-yellow)] flex justify-center">
              {balance.locked.toFixed(2)} <TonLogo size={24} />
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              {t(profileUserInfoMessages.totalBalance)}
            </p>
            <p className="text-xl font-bold flex justify-center">
              {balance.sum.toFixed(2)} <TonLogo size={24} />
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              {t(profileUserInfoMessages.myPoints)}
            </p>
            <p className="text-xl font-bold text-[var(--color-status-green)] flex justify-center items-center gap-1">
              {currentUser.points ?? 0} <Gift className="w-5 h-5" />
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
