'use client';

import { <PERSON><PERSON>, Button as TgButton } from '@telegram-apps/telegram-ui';
import { Minus, Plus, User } from 'lucide-react';
import { useIntl } from 'react-intl';

import { GlassWrapper } from '@/components/shared/glass-wrapper';
import { CompactThemeSwitcher } from '@/components/shared/theme-switcher';
import { TonConnectButton } from '@/components/shared/ton-connect-button';
import { TonLogo } from '@/components/TonLogo';
import { WithdrawDrawer } from '@/components/withdraw-drawer/withdraw-drawer';
import { cn } from '@/lib/utils';

import { DepositDrawer } from '../../../components/deposit-drawer';
import { rootLayoutHeaderMessages } from './intl/root-layout-header.messages';
import {
  useHeaderState,
  useWalletConnection,
} from './root-layout-header-hooks';

const BUTTON_CLASSES = {
  actionButton: 'min-w-6! h-6! p-0! rounded-full!',
};

export default function RootLayoutHeader() {
  const { formatMessage: t } = useIntl();
  const {
    showWalletDropdown,
    setShowWalletDropdown,
    showDepositDrawer,
    setShowDepositDrawer,
    showWithdrawDrawer,
    setShowWithdrawDrawer,
    dropdownRef,
    balanceInfo,
    onProfileButtonClick,
    currentUser,
  } = useHeaderState();

  const {
    tonWalletAddress,
    isConnecting,
    isAuthenticating,
    isLoading,
    handleWalletAction,
    handleDisconnectWallet,
    formatAddress,
  } = useWalletConnection();

  const handleDepositClick = async () => {
    if (!tonWalletAddress) {
      await handleWalletAction();
      return;
    }
    setShowDepositDrawer(true);
  };

  const handleWithdrawClick = async () => {
    if (!tonWalletAddress) {
      await handleWalletAction();
      return;
    }
    setShowWithdrawDrawer(true);
  };

  return (
    <header className="fixed top-0 left-0 right-0 text-foreground p-2 z-100">
      <GlassWrapper
        variant="default"
        intensity="light"
        className="bg-background/30 border-b border-border/50 rounded-[40px] py-3"
        enableHover={false}
      >
        <div className="flex items-center justify-between gap-1 min-w-0">
          <div className="flex items-center gap-1 bg-card/80 rounded-3xl min-w-0 p-2">
            <div className="flex items-center gap-1 min-w-0 flex-shrink">
              <TonLogo size={24} className="flex-shrink-0 -mr-0.5" />
              <div className="text-sm font-bold truncate">
                {balanceInfo.available}
              </div>
            </div>

            <div className="flex items-center gap-1 ml-1">
              <TgButton
                className={BUTTON_CLASSES.actionButton}
                onClick={handleDepositClick}
                aria-label={t(rootLayoutHeaderMessages.deposit)}
              >
                <Plus className="w-4 h-4 stroke-[2.5]" />
              </TgButton>
              <TgButton
                className={BUTTON_CLASSES.actionButton}
                onClick={handleWithdrawClick}
                aria-label={t(rootLayoutHeaderMessages.withdraw)}
              >
                <Minus className="w-4 h-4 stroke-[2.5]" />
              </TgButton>
            </div>

            <CompactThemeSwitcher className="mr-2" />

            <div
              onClick={onProfileButtonClick}
              className={cn(
                'w-10 h-10 cursor-pointer rounded-full overflow-hidden bg-card flex items-center justify-center',
                isLoading && 'pointer-events-none',
              )}
              role="button"
              aria-label={t(rootLayoutHeaderMessages.profile)}
              tabIndex={0}
            >
              {currentUser?.photoURL ? (
                <Avatar size={40} src={currentUser.photoURL} />
              ) : (
                <User className="w-4 h-4 text-muted-foreground" />
              )}
            </div>
          </div>

          <TonConnectButton
            tonWalletAddress={tonWalletAddress}
            isConnecting={isConnecting}
            isAuthenticating={isAuthenticating}
            showWalletDropdown={showWalletDropdown}
            setShowWalletDropdown={setShowWalletDropdown}
            dropdownRef={dropdownRef}
            onWalletAction={handleWalletAction}
            onDisconnectWallet={handleDisconnectWallet}
            formatAddress={formatAddress}
          />
        </div>
      </GlassWrapper>

      <DepositDrawer
        open={showDepositDrawer}
        onOpenChange={setShowDepositDrawer}
      />

      <WithdrawDrawer
        open={showWithdrawDrawer}
        onOpenChange={setShowWithdrawDrawer}
      />
    </header>
  );
}
