'use client';

import { ExternalLink, Gift } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import {
  PREM_RELAYER_URL,
  PREM_RELAYER_USERNAME,
  TELEGRAM_BOT_URL,
} from '@/core.constants';
import type { GiftEntity } from '@/mikerudenko/marketplace-shared';

import { withdrawGiftDrawerMessages } from './intl/withdraw-gift-drawer.messages';

interface WithdrawGiftDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gift: GiftEntity | null;
}

export function WithdrawGiftDrawer({
  open,
  onOpenChange,
  gift,
}: WithdrawGiftDrawerProps) {
  const { formatMessage: t } = useIntl();

  const handleOpenBot = () => {
    window.open(TELEGRAM_BOT_URL, '_blank');
  };

  const botLink = (
    <a
      href={TELEGRAM_BOT_URL}
      target="_blank"
      rel="noopener noreferrer"
      className="text-[var(--color-status-purple)] hover:text-[var(--color-status-purple)]/80 underline"
    >
      @{process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME}
    </a>
  );

  const relayerLink = (
    <a
      href={PREM_RELAYER_URL}
      target="_blank"
      rel="noopener noreferrer"
      className="text-[var(--color-status-purple)] hover:text-[var(--color-status-purple)]/80 underline"
    >
      @{PREM_RELAYER_USERNAME}
    </a>
  );

  if (!gift) return null;

  return (
    <BaseDrawer open={open} onOpenChange={onOpenChange}>
      <DrawerHeader
        icon={Gift}
        title={t(withdrawGiftDrawerMessages.withdrawGift)}
      />

      <div className="space-y-6">
        <div className="space-y-4">
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[var(--color-status-purple)] flex items-center justify-center text-white text-sm font-medium flex-shrink-0 mt-0.5">
                  1
                </div>
                <p className="text-foreground text-sm leading-relaxed">
                  {t(withdrawGiftDrawerMessages.instructionStep1, { botLink })}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[var(--color-status-purple)] flex items-center justify-center text-white text-sm font-medium flex-shrink-0 mt-0.5">
                  2
                </div>
                <p className="text-foreground text-sm leading-relaxed">
                  {t(withdrawGiftDrawerMessages.instructionStep2, {
                    relayerLink,
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 border-border text-muted-foreground hover:bg-card"
          >
            {t(withdrawGiftDrawerMessages.close)}
          </Button>
          <Button
            onClick={handleOpenBot}
            className="flex-1 bg-[var(--color-status-purple)] hover:bg-[var(--color-status-purple)]/90 text-white"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            {t(withdrawGiftDrawerMessages.openBot)}
          </Button>
        </div>
      </div>
    </BaseDrawer>
  );
}
