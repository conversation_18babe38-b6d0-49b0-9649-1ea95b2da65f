import { defineMessages } from 'react-intl';

export const createSellOrderDrawerMessages = defineMessages({
  createSellOrder: {
    id: 'createSellOrderDrawer.createSellOrder',
    defaultMessage: 'Sell',
  },
  setPrice: {
    id: 'createSellOrderDrawer.setPrice',
    defaultMessage: 'Set a price for your {giftName} gift',
  },
  priceLabel: {
    id: 'createSellOrderDrawer.priceLabel',
    defaultMessage: 'Price (TON)',
  },
  cancel: {
    id: 'createSellOrderDrawer.cancel',
    defaultMessage: 'Cancel',
  },
  createOrder: {
    id: 'createSellOrderDrawer.createOrder',
    defaultMessage: 'Create Order',
  },
  creating: {
    id: 'createSellOrderDrawer.creating',
    defaultMessage: 'Creating...',
  },
  successMessage: {
    id: 'createSellOrderDrawer.successMessage',
    defaultMessage: 'Sell order created successfully!',
  },
});
