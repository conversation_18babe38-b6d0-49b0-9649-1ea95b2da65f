import { defineMessages } from 'react-intl';

export const giftsPageMessages = defineMessages({
  youAreNotLoggedIn: {
    id: 'gifts.youAreNotLoggedIn',
    defaultMessage: 'You are not logged in',
  },
  clickLoginToSeeGifts: {
    id: 'gifts.clickLoginToSeeGifts',
    defaultMessage: 'Click on login Telegram button to see your gifts',
  },
  noGiftsFound: {
    id: 'gifts.noGiftsFound',
    defaultMessage: 'No gifts found',
  },
  myGifts: {
    id: 'gifts.myGifts',
    defaultMessage: 'My Gifts',
  },
  orderInfo: {
    id: 'gifts.orderInfo',
    defaultMessage: 'Order Info',
  },
  sellGift: {
    id: 'gifts.sellGift',
    defaultMessage: 'Sell Gift',
  },
  attachToOrder: {
    id: 'giftsPage.attachToOrder',
    defaultMessage: 'Attach to Order',
  },
  createSellOrder: {
    id: 'giftsPage.createSellOrder',
    defaultMessage: 'Sell',
  },
  withdrawGift: {
    id: 'giftsPage.withdrawGift',
    defaultMessage: 'Withdraw',
  },
});
