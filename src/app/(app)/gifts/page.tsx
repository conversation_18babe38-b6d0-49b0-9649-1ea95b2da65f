'use client';

import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { getUserGiftOrders, getUserGifts } from '@/api/gifts.api';
import { UserOrderDetailsDrawer } from '@/components/order-details/user-order-details-drawer/user-order-details-drawer';
import type { GiftEntity, OrderEntity } from '@/mikerudenko/marketplace-shared';
import { UserType } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { CreateSellOrderFromGiftDrawer } from './components/create-sell-order-from-gift-drawer';
import { GiftTile } from './components/gift-tile';
import { LinkGiftToOrderDrawer } from './components/link-gift-to-order-drawer';
import { WithdrawGiftDrawer } from './components/withdraw-gift-drawer';
import { giftsPageMessages } from './intl/gifts-page.messages';

export default function GiftsPage() {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();
  const [gifts, setGifts] = useState<GiftEntity[]>([]);
  const [giftOrders, setGiftOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedGift, setSelectedGift] = useState<GiftEntity | null>(null);
  const [showLinkGiftDrawer, setShowLinkGiftDrawer] = useState(false);
  const [selectedGiftForSell, setSelectedGiftForSell] =
    useState<GiftEntity | null>(null);
  const [showCreateSellOrderDrawer, setShowCreateSellOrderDrawer] =
    useState(false);
  const [selectedGiftForWithdraw, setSelectedGiftForWithdraw] =
    useState<GiftEntity | null>(null);
  const [showWithdrawGiftDrawer, setShowWithdrawGiftDrawer] = useState(false);

  useEffect(() => {
    const fetchGiftsData = async () => {
      if (!currentUser?.tg_id || !currentUser?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const [userGifts, userGiftOrders] = await Promise.all([
          getUserGifts(currentUser.tg_id),
          getUserGiftOrders(currentUser.id),
        ]);

        setGifts(userGifts);
        setGiftOrders(userGiftOrders);
      } catch (error) {
        console.error('Error fetching gifts data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchGiftsData();
  }, [currentUser?.tg_id, currentUser?.id]);

  const getRelatedOrder = (giftId: string) => {
    return giftOrders.find((order) => order?.gift_id_list?.includes(giftId));
  };

  const handleOrderInfoClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleSellGiftClick = (gift: GiftEntity) => {
    setSelectedGift(gift);
    setShowLinkGiftDrawer(true);
  };

  const handleCreateSellOrderClick = (gift: GiftEntity) => {
    setSelectedGiftForSell(gift);
    setShowCreateSellOrderDrawer(true);
  };

  const handleWithdrawGiftClick = (gift: GiftEntity) => {
    setSelectedGiftForWithdraw(gift);
    setShowWithdrawGiftDrawer(true);
  };

  const handleGiftLinked = () => {
    // Refetch data after gift is linked
    if (currentUser?.tg_id && currentUser?.id) {
      const fetchGiftsData = async () => {
        try {
          const [userGifts, userGiftOrders] = await Promise.all([
            getUserGifts(currentUser.tg_id!),
            getUserGiftOrders(currentUser.id!),
          ]);

          setGifts(userGifts);
          setGiftOrders(userGiftOrders);
        } catch (error) {
          console.error('Error refetching gifts data after linking:', error);
        }
      };

      fetchGiftsData();
    }
  };

  const handleOrderUpdate = () => {
    if (currentUser?.tg_id && currentUser?.id) {
      // Refetch data after order update
      const fetchGiftsData = async () => {
        try {
          const [userGifts, userGiftOrders] = await Promise.all([
            getUserGifts(currentUser.tg_id!),
            getUserGiftOrders(currentUser.id!),
          ]);

          setGifts(userGifts);
          setGiftOrders(userGiftOrders);
        } catch (error) {
          console.error('Error refetching gifts data:', error);
        }
      };

      fetchGiftsData();
    }
  };

  const getUserRole = (order: OrderEntity) => {
    if (order.buyerId === currentUser?.id) return UserType.BUYER;
    if (order.sellerId === currentUser?.id) return UserType.SELLER;
    return UserType.BUYER; // fallback
  };

  if (!currentUser) {
    return (
      <div className="container mx-auto space-y-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-4">
            {t(giftsPageMessages.youAreNotLoggedIn)}
          </h2>
          <p className="text-muted-foreground">
            {t(giftsPageMessages.clickLoginToSeeGifts)}
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-2xl font-bold mb-6">
          {t(giftsPageMessages.myGifts)}
        </h1>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {Array.from({ length: 8 }).map((_, index) => (
            <div
              key={`gift-skeleton-${index}`}
              className="bg-card border border-border rounded-lg p-2 animate-pulse"
            >
              <div className="aspect-square bg-background rounded-lg mb-2" />
              <div className="h-4 bg-border rounded mb-1" />
              <div className="h-3 bg-border rounded w-2/3" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">
        {t(giftsPageMessages.myGifts)}
      </h1>

      {gifts.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            {t(giftsPageMessages.noGiftsFound)}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 items-start">
          {gifts.map((gift) => (
            <GiftTile
              key={gift.id}
              gift={gift}
              relatedOrder={getRelatedOrder(gift.id!)}
              onOrderInfoClick={handleOrderInfoClick}
              onSellGiftClick={handleSellGiftClick}
              onCreateSellOrderClick={handleCreateSellOrderClick}
              onWithdrawGiftClick={handleWithdrawGiftClick}
            />
          ))}
        </div>
      )}

      <UserOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={selectedOrder ? getUserRole(selectedOrder) : UserType.BUYER}
        onOrderUpdate={handleOrderUpdate}
      />

      <LinkGiftToOrderDrawer
        open={showLinkGiftDrawer}
        onOpenChange={setShowLinkGiftDrawer}
        gift={selectedGift}
        onGiftLinked={handleGiftLinked}
      />

      <CreateSellOrderFromGiftDrawer
        open={showCreateSellOrderDrawer}
        onOpenChange={setShowCreateSellOrderDrawer}
        gift={selectedGiftForSell}
        onOrderCreated={handleGiftLinked}
      />

      <WithdrawGiftDrawer
        open={showWithdrawGiftDrawer}
        onOpenChange={setShowWithdrawGiftDrawer}
        gift={selectedGiftForWithdraw}
      />
    </div>
  );
}
