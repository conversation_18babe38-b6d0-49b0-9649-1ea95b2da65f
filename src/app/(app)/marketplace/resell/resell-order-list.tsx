'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { Hash, TrendingUp } from 'lucide-react';

import { OrderImage } from '@/components/shared/order-image';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';

interface ResellOrderListProps {
  orders: OrderEntity[];
  collections: CollectionEntity[];
  onOrderSelect: (order: OrderEntity) => void;
}

export function ResellOrderList({
  orders,
  collections,
  onOrderSelect,
}: ResellOrderListProps) {
  const getCollection = (collectionId: string) => {
    return collections.find((c) => c.id === collectionId);
  };

  return (
    <div className="space-y-3 max-h-[50vh] overflow-y-auto">
      {orders.map((order) => {
        const collection = getCollection(order.collectionId);

        return (
          <div
            key={order.id}
            className="bg-card rounded-xl p-4 border border-border hover:border-border/80 transition-colors"
          >
            <div className="flex gap-3 mb-3">
              <div className="flex-shrink-0">
                <OrderImage
                  order={order}
                  collection={collection}
                  className="w-16 h-16"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Hash className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-foreground">
                      {order.number || order.id?.slice(-6)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TonLogo size={20} />
                    <span className="text-lg font-bold text-foreground">
                      {order.price.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div>
                  <Caption
                    level="2"
                    weight="3"
                    className="text-muted-foreground mb-1"
                  >
                    Collection
                  </Caption>
                  <p className="text-foreground font-medium truncate">
                    {collection?.name || 'Unknown Collection'}
                  </p>
                </div>
              </div>
            </div>

            <Button
              onClick={() => onOrderSelect(order)}
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl py-2 font-semibold flex items-center justify-center gap-2"
            >
              <TrendingUp className="w-4 h-4" />
              Set Resale Price
            </Button>
          </div>
        );
      })}
    </div>
  );
}
