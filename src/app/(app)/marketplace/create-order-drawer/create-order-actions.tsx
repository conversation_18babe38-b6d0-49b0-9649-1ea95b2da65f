'use client';

import { useIntl } from 'react-intl';

import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';

import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderActionsProps {
  onCreateOrder: () => void;
  onCancel: () => void;
  isValidPrice: boolean;
  hasSufficientBalance: boolean;
  loading: boolean;
  price: number;
}

export function CreateOrderActions({
  onCreateOrder,
  onCancel,
  isValidPrice,
  hasSufficientBalance,
  loading,
  price,
}: CreateOrderActionsProps) {
  const { formatMessage: t } = useIntl();

  const isDisabled = !isValidPrice || !hasSufficientBalance || loading;

  return (
    <div className="space-y-3 pt-4">
      <ConfirmWrapper>
        <Button
          onClick={onCreateOrder}
          disabled={isDisabled}
          className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground border-0 rounded-2xl"
        >
          {loading ? (
            t(createOrderDrawerMessages.creating)
          ) : (
            <>
              {t(createOrderDrawerMessages.create)}
              {isValidPrice && (
                <>
                  {' '}
                  &#40;{price.toFixed(2)} <TonLogo className="-m-2" size={24} />
                  <span className="-ml-1">&#41;</span>
                </>
              )}
            </>
          )}
        </Button>
      </ConfirmWrapper>

      <Button
        variant="outline"
        onClick={onCancel}
        className="w-full h-12 bg-transparent border-border/50 text-muted-foreground hover:bg-card/50 hover:text-foreground rounded-2xl"
        disabled={loading}
      >
        {t(createOrderDrawerMessages.cancel)}
      </Button>
    </div>
  );
}
