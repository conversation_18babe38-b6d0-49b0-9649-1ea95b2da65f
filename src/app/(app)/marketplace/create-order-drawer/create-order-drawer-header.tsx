'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { ShoppingCart } from 'lucide-react';
import { useIntl } from 'react-intl';

import { UserType } from '@/mikerudenko/marketplace-shared';

import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderDrawerHeaderProps {
  userType: UserType;
}

export function CreateOrderDrawerHeader({
  userType,
}: CreateOrderDrawerHeaderProps) {
  const { formatMessage: t } = useIntl();

  const isSeller = userType === UserType.SELLER;

  return (
    <div className="flex items-center gap-3 mb-6">
      <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
        <ShoppingCart className="w-5 h-5 text-primary-foreground" />
      </div>
      <div>
        <h2 className="text-xl font-semibold text-foreground">
          {t(
            isSeller
              ? createOrderDrawerMessages.createSellOrder
              : createOrderDrawerMessages.createBuyOrder,
          )}
        </h2>
        <Caption level="2" weight="3" className="text-muted-foreground">
          {t(
            isSeller
              ? createOrderDrawerMessages.sellOrderSubtitle
              : createOrderDrawerMessages.buyOrderSubtitle,
          )}
        </Caption>
      </div>
    </div>
  );
}
