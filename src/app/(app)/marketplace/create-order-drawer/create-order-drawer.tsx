'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { httpsCallable } from 'firebase/functions';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { useLocalStorage } from 'usehooks-ts';
import { Drawer } from 'vaul';

import { getGiftsAvailableForLinkingToOrder } from '@/api/gifts.api';
import { formatServerError } from '@/api/server-error-handler';
import { DepositDrawer } from '@/components/deposit-drawer';
import { InsufficientBalance } from '@/components/ui/insufficient-balance';
import {
  AppCloudFunctions,
  BASE_TG_UI_STYLES,
  LocalStorageKeys,
} from '@/core.constants';
import { useVisualViewport } from '@/hooks/use-visual-viewport';
import { bpsToDecimal } from '@/lib/utils';
import {
  CollectionStatus,
  type GiftEntity,
  type OrderEntity,
  UserType,
} from '@/mikerudenko/marketplace-shared';
import { firebaseFunctions, useRootContext } from '@/root-context';

import { useWalletConnection } from '../../root-layout-header/root-layout-header-hooks';
import { CreateOrderPriceDetails } from '../create-order-price-details';
import { GiftSelectionDrawer } from '../gift-selection-drawer';
import { CreateOrderActions } from './create-order-actions';
import { CreateOrderCollectionSection } from './create-order-collection-section';
import { CreateOrderDrawerHeader } from './create-order-drawer-header';
import { CreateOrderInfoSection } from './create-order-info-section';
import { CreateOrderPriceSection } from './create-order-price-section';
import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userType: UserType;
  onOrderCreated?: () => void;
}

export function CreateOrderDrawer({
  open,
  onOpenChange,
  userType,
  onOrderCreated,
}: CreateOrderDrawerProps) {
  const { formatMessage: t } = useIntl();
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );
  const { currentUser, collections, appConfig } = useRootContext();
  const [selectedCollection, setSelectedCollection] = useState('');
  const [itemPrice, setItemPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);
  const [availableGifts, setAvailableGifts] = useState<GiftEntity[]>([]);
  const [selectedGift, setSelectedGift] = useState<GiftEntity | null>(null);
  const [showGiftSelectionDrawer, setShowGiftSelectionDrawer] = useState(false);
  const [loadingGifts, setLoadingGifts] = useState(false);
  const drawerContentRef = useVisualViewport({ enabled: open, offset: 64 });

  const { tonWalletAddress, handleWalletAction } = useWalletConnection();

  const selectedCollectionData = collections.find(
    (c) => c.id === selectedCollection,
  );
  const price = parseFloat(itemPrice);
  const isValidPrice =
    !isNaN(price) &&
    price > 0 &&
    selectedCollectionData &&
    price >= selectedCollectionData.floorPrice;

  const lockPercentageBPS =
    userType === UserType.SELLER
      ? (appConfig?.seller_lock_percentage as number)
      : (appConfig?.buyer_lock_percentage as number);

  const lockPercentage = bpsToDecimal(lockPercentageBPS);
  const lockAmount = isValidPrice ? price * lockPercentage : 0;
  const availableBalance = currentUser?.balance
    ? currentUser.balance.sum - currentUser.balance.locked
    : 0;
  const hasSufficientBalance = lockAmount <= availableBalance;
  const areLockAndCollateralHidden =
    !selectedCollectionData ||
    selectedCollectionData?.status === CollectionStatus.MARKET;

  const handleTopUp = async () => {
    if (!tonWalletAddress) {
      await handleWalletAction();
      return;
    }
    setShowDepositDrawer(true);
  };

  const fetchAvailableGifts = async (collectionId: string) => {
    if (!currentUser?.tg_id) return;

    setLoadingGifts(true);
    try {
      const gifts = await getGiftsAvailableForLinkingToOrder(
        currentUser.tg_id,
        collectionId,
      );
      setAvailableGifts(gifts);
    } catch (error) {
      console.error('Error fetching available gifts:', error);
      toast.error(t(createOrderDrawerMessages.failedToLoadGifts));
    } finally {
      setLoadingGifts(false);
    }
  };

  const handleCollectionChange = (collectionId: string) => {
    setSelectedCollection(collectionId);
    setSelectedGift(null);
    setAvailableGifts([]);

    const collection = collections.find((c) => c.id === collectionId);
    if (
      collection?.status === CollectionStatus.MARKET &&
      userType === UserType.SELLER
    ) {
      fetchAvailableGifts(collectionId);
    }
  };

  const handleGiftSelect = (gift: GiftEntity) => {
    setSelectedGift(gift);
    setShowGiftSelectionDrawer(false);
  };

  const handleCreateOrder = async () => {
    if (!isValidPrice || !selectedCollectionData || !currentUser) {
      toast.error(t(createOrderDrawerMessages.fillRequiredFields));
      return;
    }

    if (!hasSufficientBalance) {
      toast.error(t(createOrderDrawerMessages.insufficientAvailableBalance));
      return;
    }

    if (
      selectedCollectionData.status === CollectionStatus.MARKET &&
      userType === UserType.SELLER &&
      !selectedGift
    ) {
      toast.error(t(createOrderDrawerMessages.selectGiftRequired));
      return;
    }

    try {
      setLoading(true);

      const functionName =
        userType === UserType.SELLER
          ? AppCloudFunctions.createOrderAsSeller
          : AppCloudFunctions.createOrderAsBuyer;
      const createOrderFunction = httpsCallable(
        firebaseFunctions,
        functionName,
      );

      const orderData: Partial<OrderEntity> = {
        collectionId: selectedCollection,
        price,
        ...(userType === UserType.SELLER
          ? { sellerId: currentUser.id }
          : { buyerId: currentUser.id }),
      };

      if (
        selectedCollectionData.status === CollectionStatus.MARKET &&
        userType === UserType.SELLER &&
        selectedGift
      ) {
        orderData.gift_id_list = [selectedGift.id as string];
      }

      const result = await createOrderFunction(orderData);

      const message =
        result.data &&
        typeof result.data === 'object' &&
        'message' in result.data
          ? (result.data as { message: string }).message
          : t(createOrderDrawerMessages.orderCreatedSuccess);
      toast.success(message);

      handleClose();

      if (onOrderCreated) {
        onOrderCreated();
      }
    } catch (error: unknown) {
      console.error('Order creation failed:', error);
      const errorMessage = formatServerError(error, t);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedCollection('');
    setItemPrice('');
    setSelectedGift(null);
    setAvailableGifts([]);
    onOpenChange(false);
  };

  const onDrawerChange = (open: boolean) => {
    if (!open) {
      handleClose();
    }

    onOpenChange(open);
  };

  return (
    <>
      <Drawer.Root
        {...{
          open,
          onOpenChange: onDrawerChange,
        }}
        shouldScaleBackground
        modal={true}
        dismissible={true}
      >
        <Drawer.Portal>
          <Drawer.Title />
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
          <Drawer.Content
            ref={drawerContentRef}
            className="bg-background flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none max-h-[90vh]"
            style={BASE_TG_UI_STYLES as React.CSSProperties}
          >
            <div className="p-6 bg-background rounded-t-[20px] flex-1 max-h-[90vh] overflow-y-auto">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-muted-foreground mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

              <div className="max-w-md mx-auto space-y-6">
                <CreateOrderDrawerHeader userType={userType} />

                {!appConfig ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <Caption
                      level="2"
                      weight="3"
                      className="text-muted-foreground mt-2"
                    >
                      {t(createOrderDrawerMessages.loadingConfiguration)}
                    </Caption>
                  </div>
                ) : (
                  <>
                    <CreateOrderInfoSection
                      lockPercentage={lockPercentage}
                      availableBalance={availableBalance}
                      areLockAndCollateralHidden={areLockAndCollateralHidden}
                    />

                    <div className="space-y-4">
                      <CreateOrderCollectionSection
                        collections={collections}
                        selectedCollection={selectedCollection}
                        onCollectionChange={handleCollectionChange}
                        selectedCollectionData={selectedCollectionData}
                        userType={userType}
                        availableGifts={availableGifts}
                        selectedGift={selectedGift}
                        loadingGifts={loadingGifts}
                        onShowGiftSelection={() =>
                          setShowGiftSelectionDrawer(true)
                        }
                        isAnimatedCollection={isAnimatedCollection}
                      />

                      <CreateOrderPriceSection
                        itemPrice={itemPrice}
                        onPriceChange={setItemPrice}
                        selectedCollectionData={selectedCollectionData}
                        price={price}
                      />

                      {isValidPrice && (
                        <CreateOrderPriceDetails
                          areLockAndCollateralHidden={
                            areLockAndCollateralHidden
                          }
                          price={price}
                          lockPercentage={lockPercentage}
                          lockAmount={lockAmount}
                          availableBalance={availableBalance}
                          hasSufficientBalance={hasSufficientBalance}
                        />
                      )}

                      {!hasSufficientBalance && isValidPrice && (
                        <InsufficientBalance
                          message={t(
                            createOrderDrawerMessages.insufficientBalanceMessage,
                            {
                              amount: lockAmount.toFixed(2),
                            },
                          )}
                          onTopUp={handleTopUp}
                          className="mt-4"
                        />
                      )}

                      <CreateOrderActions
                        onCreateOrder={handleCreateOrder}
                        onCancel={handleClose}
                        isValidPrice={!!isValidPrice}
                        hasSufficientBalance={hasSufficientBalance}
                        loading={loading}
                        price={price}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>

      <GiftSelectionDrawer
        open={showGiftSelectionDrawer}
        onOpenChange={setShowGiftSelectionDrawer}
        gifts={availableGifts}
        onGiftSelect={handleGiftSelect}
      />

      <DepositDrawer
        open={showDepositDrawer}
        onOpenChange={setShowDepositDrawer}
      />
    </>
  );
}
