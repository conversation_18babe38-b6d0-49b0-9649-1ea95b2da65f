import { defineMessages } from 'react-intl';

export const createOrderDrawerMessages = defineMessages({
  createSellOrder: {
    id: 'marketplace.createOrder.createSellOrder',
    defaultMessage: 'Sell',
  },
  createBuyOrder: {
    id: 'marketplace.createOrder.createBuyOrder',
    defaultMessage: 'Create Buy Order',
  },
  sellOrderSubtitle: {
    id: 'marketplace.createOrder.sellOrderSubtitle',
    defaultMessage: 'List your item for sale',
  },
  buyOrderSubtitle: {
    id: 'marketplace.createOrder.buyOrderSubtitle',
    defaultMessage: 'Place a buy order for an item',
  },
  orderInformation: {
    id: 'marketplace.createOrder.orderInformation',
    defaultMessage: 'Order Information',
  },
  lockPercentage: {
    id: 'marketplace.createOrder.lockPercentage',
    defaultMessage: 'Lock percentage:',
  },
  availableBalance: {
    id: 'marketplace.createOrder.availableBalance',
    defaultMessage: 'Available balance:',
  },
  loadingConfiguration: {
    id: 'marketplace.createOrder.loadingConfiguration',
    defaultMessage: 'Loading configuration...',
  },
  marketCollectionNotice: {
    id: 'marketplace.createOrder.marketCollectionNotice',
    defaultMessage: 'Market Collection Notice',
  },
  marketCollectionDescription: {
    id: 'marketplace.createOrder.marketCollectionDescription',
    defaultMessage:
      "After creating your order, you'll need to send your gift to the relayer to activate this order.",
  },
  selectGiftToAttach: {
    id: 'marketplace.createOrder.selectGiftToAttach',
    defaultMessage: 'Select Gift to Attach (Optional)',
  },
  loadingAvailableGifts: {
    id: 'marketplace.createOrder.loadingAvailableGifts',
    defaultMessage: 'Loading available gifts...',
  },
  noGiftsAvailable: {
    id: 'marketplace.createOrder.noGiftsAvailable',
    defaultMessage:
      'No gifts available for this collection. Please deposit a gift to the bot first.',
  },
  selectedGift: {
    id: 'marketplace.createOrder.selectedGift',
    defaultMessage: 'Selected: {giftName} #{giftSymbol}',
  },
  selectGiftButton: {
    id: 'marketplace.createOrder.selectGiftButton',
    defaultMessage: 'Select Gift ({count} available)',
  },
  itemPriceLabel: {
    id: 'marketplace.createOrder.itemPriceLabel',
    defaultMessage: 'Item Price (TON)',
  },
  enterPrice: {
    id: 'marketplace.createOrder.enterPrice',
    defaultMessage: 'Enter price',
  },
  minimumPrice: {
    id: 'marketplace.createOrder.minimumPrice',
    defaultMessage: 'Min {amount} TON',
  },
  priceFloorError: {
    id: 'marketplace.createOrder.priceFloorError',
    defaultMessage: 'Price must be at least {amount} TON (floor price)',
  },
  insufficientBalanceMessage: {
    id: 'marketplace.createOrder.insufficientBalanceMessage',
    defaultMessage: 'Insufficient balance to lock {amount} TON',
  },
  create: {
    id: 'marketplace.createOrder.create',
    defaultMessage: 'Create',
  },
  creating: {
    id: 'marketplace.createOrder.creating',
    defaultMessage: 'Creating...',
  },
  cancel: {
    id: 'marketplace.createOrder.cancel',
    defaultMessage: 'Cancel',
  },
  fillRequiredFields: {
    id: 'marketplace.createOrder.fillRequiredFields',
    defaultMessage: 'Please fill in all required fields',
  },
  insufficientAvailableBalance: {
    id: 'marketplace.createOrder.insufficientAvailableBalance',
    defaultMessage: 'Insufficient available balance',
  },
  selectGiftRequired: {
    id: 'marketplace.createOrder.selectGiftRequired',
    defaultMessage: 'Please select a gift to attach to this order',
  },
  orderCreatedSuccess: {
    id: 'marketplace.createOrder.orderCreatedSuccess',
    defaultMessage: 'Order created successfully!',
  },
  failedToLoadGifts: {
    id: 'marketplace.createOrder.failedToLoadGifts',
    defaultMessage: 'Failed to load available gifts',
  },
  selectCollection: {
    id: 'marketplace.createOrder.selectCollection',
    defaultMessage: 'Select a collection',
  },
  selectGift: {
    id: 'marketplace.createOrder.selectGift',
    defaultMessage: 'Select Gift',
  },
  selectGiftSubtitle: {
    id: 'marketplace.createOrder.selectGiftSubtitle',
    defaultMessage: 'Choose a gift to attach to your order',
  },
  backdrop: {
    id: 'marketplace.createOrder.backdrop',
    defaultMessage: 'Backdrop:',
  },
});
