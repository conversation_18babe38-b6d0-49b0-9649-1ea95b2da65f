import { PriceLabel } from '@/components/shared/price-label';
import { TonLogo } from '@/components/TonLogo';

interface CreateOrderPriceDetailsProps {
  price: number;
  lockPercentage: number;
  lockAmount: number;
  availableBalance: number;
  hasSufficientBalance: boolean;
  areLockAndCollateralHidden?: boolean;
}

export function CreateOrderPriceDetails({
  price,
  lockPercentage,
  lockAmount,
  availableBalance,
  hasSufficientBalance,
  areLockAndCollateralHidden = false,
}: CreateOrderPriceDetailsProps) {
  return (
    <div className="bg-card/50 rounded-2xl p-4 border border-border/30">
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-muted-foreground">Item price:</span>
          <PriceLabel
            amount={price}
            size={24}
            className="text-primary font-semibold"
            clickable={false}
          />
        </div>
        {!areLockAndCollateralHidden && (
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">
              Your Collateral Locked Percentage (
              {(lockPercentage * 100).toFixed(0)}
              %):
            </span>
            <PriceLabel
              amount={lockAmount}
              size={24}
              className="text-primary font-semibold"
              clickable={false}
            />
          </div>
        )}
        <div className="flex justify-between items-center">
          <span className="text-muted-foreground">Available balance:</span>
          <PriceLabel
            amount={availableBalance}
            size={24}
            className="text-primary font-semibold"
            clickable={false}
          />
        </div>
        <div className="flex justify-between items-center font-medium border-t border-border/30 pt-3">
          <span className="text-foreground">Remaining after lock:</span>
          <div className="flex items-center gap-1">
            <span
              className={`font-semibold ${
                hasSufficientBalance ? 'text-primary' : 'text-destructive'
              }`}
            >
              {(availableBalance - lockAmount).toFixed(2)}
            </span>
            <TonLogo size={24} />
          </div>
        </div>
      </div>
    </div>
  );
}
