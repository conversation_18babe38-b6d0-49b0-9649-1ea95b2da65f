import { defineMessages } from 'react-intl';

export const cancelOrderDrawerMessages = defineMessages({
  cancelOrder: {
    id: 'orders.cancelOrder.cancelOrder',
    defaultMessage: 'Cancel Order',
  },
  confirmCancellation: {
    id: 'orders.cancelOrder.confirmCancellation',
    defaultMessage: 'Are you sure you want to cancel this order?',
  },
  cancellationWarning: {
    id: 'orders.cancelOrder.cancellationWarning',
    defaultMessage: 'This action cannot be undone.',
  },
  collateralLossWarning: {
    id: 'orders.cancelOrder.collateralLossWarning',
    defaultMessage: 'You will lose {amount} TON in collateral.',
  },
  resellerEarningsWarning: {
    id: 'orders.cancelOrder.resellerEarningsWarning',
    defaultMessage: 'As a reseller, you will lose your potential earnings.',
  },
  cancelling: {
    id: 'orders.cancelOrder.cancelling',
    defaultMessage: 'Cancelling...',
  },
  cancel: {
    id: 'orders.cancelOrder.cancel',
    defaultMessage: 'Cancel',
  },
  keepOrder: {
    id: 'orders.cancelOrder.keepOrder',
    defaultMessage: 'Keep Order',
  },
  orderCancelledSuccessfully: {
    id: 'orders.cancelOrder.orderCancelledSuccessfully',
    defaultMessage: 'Order cancelled successfully',
  },
  failedToCancelOrder: {
    id: 'orders.cancelOrder.failedToCancelOrder',
    defaultMessage: 'Failed to cancel order: {message}',
  },
  unexpectedError: {
    id: 'orders.cancelOrder.unexpectedError',
    defaultMessage: 'An unexpected error occurred',
  },
  collateralLost: {
    id: 'orders.cancelOrder.collateralLost',
    defaultMessage: 'Collateral Lost',
  },
  warningPenaltyFee: {
    id: 'orders.cancelOrder.warningPenaltyFee',
    defaultMessage: 'Warning: Penalty Fee Will Be Applied',
  },
  collateralLossDescription: {
    id: 'orders.cancelOrder.collateralLossDescription',
    defaultMessage:
      'You will lose {amount} TON in collateral. This action is permanent and cannot be undone.',
  },
  penaltyFeeDescription: {
    id: 'orders.cancelOrder.penaltyFeeDescription',
    defaultMessage:
      'If you cancel this order, a penalty fee of {fee} TON will be taken from your balance. This action is permanent and cannot be undone.',
  },
  resellerEarningsLoss: {
    id: 'orders.cancelOrder.resellerEarningsLoss',
    defaultMessage:
      'Additionally, you will lose {amount} TON in reseller earnings.',
  },
});
