import { defineMessages } from 'react-intl';

export const fulfillAndResellDrawerMessages = defineMessages({
  title: {
    id: 'fulfillAndResellDrawer.title',
    defaultMessage: 'Fulfill & Resell Order',
  },
  resellInformation: {
    id: 'fulfillAndResellDrawer.resellInformation',
    defaultMessage: 'Resell Order Information',
  },
  lockPercentage: {
    id: 'fulfillAndResellDrawer.lockPercentage',
    defaultMessage: 'Lock percentage:',
  },
  availableBalance: {
    id: 'fulfillAndResellDrawer.availableBalance',
    defaultMessage: 'Available balance:',
  },
  resellPrice: {
    id: 'fulfillAndResellDrawer.resellPrice',
    defaultMessage: 'Resell Price (TON)',
  },
  lockAmount: {
    id: 'fulfillAndResellDrawer.lockAmount',
    defaultMessage: 'Lock amount:',
  },
  insufficientBalance: {
    id: 'fulfillAndResellDrawer.insufficientBalance',
    defaultMessage: 'Insufficient balance to lock {amount} TON',
  },
  fulfillAndResell: {
    id: 'fulfillAndResellDrawer.fulfillAndResell',
    defaultMessage: 'Fulfill & Resell',
  },
  processing: {
    id: 'fulfillAndResellDrawer.processing',
    defaultMessage: 'Processing...',
  },
  cancel: {
    id: 'fulfillAndResellDrawer.cancel',
    defaultMessage: 'Cancel',
  },
  successMessage: {
    id: 'fulfillAndResellDrawer.successMessage',
    defaultMessage: 'Order fulfilled and resell order created successfully!',
  },
});
