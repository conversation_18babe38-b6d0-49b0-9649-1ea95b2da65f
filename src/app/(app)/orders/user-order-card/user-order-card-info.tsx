import { Caption } from '@telegram-apps/telegram-ui';

import { CollectionName } from '@/components/shared/collection-name';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';
import { getOrderDisplayNumber } from '@/services/order-service';

interface UserOrderCardInfoProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
  className?: string;
}

export function UserOrderCardInfo({
  order,
  collection,
  className,
}: UserOrderCardInfoProps) {
  return (
    <div className={cn('flex items-center justify-between', className)}>
      <Caption level="1" weight="1" className="truncate">
        <CollectionName collection={collection} />
      </Caption>
      <Caption level="2" weight="3" className="w-fit text-muted-foreground">
        {getOrderDisplayNumber(order)}
      </Caption>
    </div>
  );
}
