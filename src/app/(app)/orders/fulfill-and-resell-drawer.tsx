'use client';

import { httpsCallable } from 'firebase/functions';
import { AlertTriangle, Gift } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

import { formatServerError } from '@/api/server-error-handler';
import { DepositDrawer } from '@/components/deposit-drawer';
import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { InsufficientBalance } from '@/components/ui/insufficient-balance';
import { Label } from '@/components/ui/label';
import { AppCloudFunctions, BASE_TG_UI_STYLES } from '@/core.constants';
import { useVisualViewport } from '@/hooks/use-visual-viewport';
import { bpsToDecimal } from '@/lib/utils';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import { firebaseFunctions, useRootContext } from '@/root-context';

import { useWalletConnection } from '../root-layout-header/root-layout-header-hooks';
import { fulfillAndResellDrawerMessages } from './intl/fulfill-and-resell-drawer.messages';

interface FulfillAndResellDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderResold?: () => void;
}

export function FulfillAndResellDrawer({
  open,
  onOpenChange,
  order,
  onOrderResold,
}: FulfillAndResellDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, appConfig } = useRootContext();
  const [resellPrice, setResellPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);
  const drawerContentRef = useVisualViewport({ enabled: open, offset: 64 });

  const { tonWalletAddress, handleWalletAction } = useWalletConnection();

  const price = parseFloat(resellPrice);
  const isValidPrice = !isNaN(price) && price > 0;

  const sellerLockPercentageBPS = appConfig?.seller_lock_percentage || 0;
  const sellerLockPercentage = bpsToDecimal(sellerLockPercentageBPS);
  const lockAmount = isValidPrice ? price * sellerLockPercentage : 0;
  const availableBalance = currentUser?.balance
    ? currentUser.balance.sum - currentUser.balance.locked
    : 0;
  const hasSufficientBalance = lockAmount <= availableBalance;

  const handleTopUp = async () => {
    if (!tonWalletAddress) {
      await handleWalletAction();
      return;
    }
    setShowDepositDrawer(true);
  };

  const handleFulfillAndResell = async () => {
    if (!isValidPrice || !order || !currentUser) {
      toast.error('Please enter a valid price');
      return;
    }

    if (!hasSufficientBalance) {
      toast.error('Insufficient available balance');
      return;
    }

    try {
      setLoading(true);

      const fulfillAndResellFunction = httpsCallable(
        firebaseFunctions,
        AppCloudFunctions.fulfillOrderAndCreateResellOrder,
      );

      await fulfillAndResellFunction({
        orderId: order.id,
        price,
      });

      toast.success(t(fulfillAndResellDrawerMessages.successMessage));

      setResellPrice('');
      onOpenChange(false);

      if (onOrderResold) {
        onOrderResold();
      }
    } catch (error) {
      console.error('Error fulfilling and reselling order:', error);
      const errorMessage = formatServerError(error, t);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setResellPrice('');
    onOpenChange(false);
  };

  if (!order) {
    return null;
  }

  return (
    <>
      <Drawer.Root
        {...{
          open,
          onOpenChange,
        }}
        shouldScaleBackground
        modal={true}
        dismissible={true}
      >
        <Drawer.Portal>
          <Drawer.Title />
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
          <Drawer.Content
            ref={drawerContentRef}
            className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none max-h-[90vh]"
            style={BASE_TG_UI_STYLES as React.CSSProperties}
          >
            <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 max-h-[90vh] overflow-y-auto">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

              <div className="max-w-md mx-auto space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-[#6ab2f2] rounded-full flex items-center justify-center">
                    <Gift className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-[#f5f5f5]">
                      {t(fulfillAndResellDrawerMessages.title)}
                    </h2>
                  </div>
                </div>

                <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                  <div className="w-full flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-[#6ab2f2] flex-shrink-0 mt-0.5" />
                    <div className="w-full text-sm">
                      <p className="w-full font-medium text-[#f5f5f5] mb-2">
                        {t(fulfillAndResellDrawerMessages.resellInformation)}
                      </p>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center py-1">
                          <span className="text-[#708499]">
                            {t(fulfillAndResellDrawerMessages.lockPercentage)}
                          </span>
                          <span className="text-[#6ab2f2] font-semibold">
                            {(sellerLockPercentage * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-[#708499]">
                            {t(fulfillAndResellDrawerMessages.availableBalance)}
                          </span>
                          <div className="flex items-center gap-1">
                            <span className="text-primary font-semibold">
                              {availableBalance.toFixed(2)}
                            </span>
                            <TonLogo size={24} />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <Label
                    htmlFor="resell-price"
                    className="text-sm font-medium text-foreground"
                  >
                    {t(fulfillAndResellDrawerMessages.resellPrice)}
                  </Label>
                  <Input
                    id="resell-price"
                    type="number"
                    step="0.01"
                    placeholder="Enter resell price"
                    value={resellPrice}
                    onChange={(e) => setResellPrice(e.target.value)}
                    className="mt-2 bg-card/50 border-border/50 text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/20"
                    min={0}
                  />
                </div>

                {isValidPrice && (
                  <div className="bg-card/50 rounded-2xl p-4 border border-border/30">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">
                          {t(fulfillAndResellDrawerMessages.resellPrice)}
                        </span>
                        <div className="flex items-center gap-1">
                          <span className="text-foreground font-semibold">
                            {price.toFixed(2)}
                          </span>
                          <TonLogo size={20} />
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">
                          {t(fulfillAndResellDrawerMessages.lockAmount)}
                        </span>
                        <div className="flex items-center gap-1">
                          <span className="text-foreground font-semibold">
                            {lockAmount.toFixed(2)}
                          </span>
                          <TonLogo size={20} />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {!hasSufficientBalance && isValidPrice && (
                  <InsufficientBalance
                    message={t(
                      fulfillAndResellDrawerMessages.insufficientBalance,
                      {
                        amount: lockAmount.toFixed(2),
                      },
                    )}
                    onTopUp={handleTopUp}
                    className="mt-4"
                  />
                )}

                <div className="space-y-3 pt-4">
                  <ConfirmWrapper>
                    <Button
                      onClick={handleFulfillAndResell}
                      disabled={
                        !isValidPrice || !hasSufficientBalance || loading
                      }
                      className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground border-0 rounded-2xl"
                    >
                      {loading ? (
                        t(fulfillAndResellDrawerMessages.processing)
                      ) : (
                        <>
                          {t(fulfillAndResellDrawerMessages.fulfillAndResell)}
                          {isValidPrice && (
                            <>
                              {' '}
                              &#40;{price.toFixed(2)}{' '}
                              <TonLogo className="-m-2" size={24} />
                              <span className="-ml-1">&#41;</span>
                            </>
                          )}
                        </>
                      )}
                    </Button>
                  </ConfirmWrapper>

                  <Button
                    onClick={handleClose}
                    variant="outline"
                    className="w-full h-12 bg-transparent border-[#3a4a5c]/50 text-[#708499] hover:bg-[#232e3c]/50 hover:text-[#f5f5f5] rounded-2xl"
                    disabled={loading}
                  >
                    {t(fulfillAndResellDrawerMessages.cancel)}
                  </Button>
                </div>
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>

      <DepositDrawer
        open={showDepositDrawer}
        onOpenChange={setShowDepositDrawer}
      />
    </>
  );
}
