'use client';

import { ThemeSwitcher } from '@/components/shared/theme-switcher';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useThemeSwitcher } from '@/hooks/use-theme-switcher';

export default function ThemeDemoPage() {
  const { theme, getThemeLabel } = useThemeSwitcher();

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-foreground">Theme Demo Page</h1>
        <p className="text-muted-foreground">
          Current theme:{' '}
          <span className="font-semibold">{getThemeLabel(theme)}</span>
        </p>
        <ThemeSwitcher variant="dropdown" className="mx-auto" />
      </div>

      {/* Color Showcase */}
      <Card>
        <CardHeader>
          <CardTitle>Color Showcase</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Semantic Colors */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Semantic Colors</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-primary text-primary-foreground p-3 rounded">
                Primary
              </div>
              <div className="bg-secondary text-secondary-foreground p-3 rounded">
                Secondary
              </div>
              <div className="bg-accent text-accent-foreground p-3 rounded">
                Accent
              </div>
              <div className="bg-destructive text-destructive-foreground p-3 rounded">
                Destructive
              </div>
            </div>
          </div>

          {/* Status Colors */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Status Colors</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-[var(--color-status-green-bg)] text-[var(--color-status-green)] border border-[var(--color-status-green-border)] p-3 rounded">
                Success
              </div>
              <div className="bg-[var(--color-status-yellow-bg)] text-[var(--color-status-yellow)] border border-[var(--color-status-yellow-border)] p-3 rounded">
                Warning
              </div>
              <div className="bg-[var(--color-status-blue-bg)] text-[var(--color-status-blue)] border border-[var(--color-status-blue-border)] p-3 rounded">
                Info
              </div>
              <div className="bg-[var(--color-status-orange-bg)] text-[var(--color-status-orange)] border border-[var(--color-status-orange-border)] p-3 rounded">
                Error
              </div>
            </div>
          </div>

          {/* Transaction Colors */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Transaction Colors</h3>
            <div className="flex gap-4">
              <div className="text-[var(--color-transaction-positive)] font-semibold">
                +100.50 TON (Positive)
              </div>
              <div className="text-[var(--color-transaction-negative)] font-semibold">
                -50.25 TON (Negative)
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Component Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Component Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Buttons */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Buttons</h3>
            <div className="flex flex-wrap gap-2">
              <Button variant="default">Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
          </div>

          {/* Badges */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Badges</h3>
            <div className="flex flex-wrap gap-2">
              <Badge variant="default">Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="outline">Outline</Badge>
            </div>
          </div>

          {/* Cards */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Cards</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Card 1</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    This is a sample card with muted text.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Card 2</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Another card to show consistency.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Card 3</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    All cards adapt to the current theme.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Information */}
      <Card>
        <CardHeader>
          <CardTitle>Theme Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>Current Theme:</strong> {getThemeLabel(theme)}
            </p>
            <p>
              <strong>Background:</strong> <code>var(--color-background)</code>
            </p>
            <p>
              <strong>Foreground:</strong> <code>var(--color-foreground)</code>
            </p>
            <p>
              <strong>Card Background:</strong> <code>var(--color-card)</code>
            </p>
            <p>
              <strong>Border:</strong> <code>var(--color-border)</code>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
