export enum NotificationType {
  SELLER_ORDER_PAID = 'SELLER_ORDER_PAID',
  BUYER_GIFT_SENT = 'BUYER_GIFT_SENT',
  SELLER_NEW_PROPOSAL = 'SELLER_NEW_PROPOSAL',
  PROPOSER_ACCEPTED = 'PROPOSER_ACCEPTED',
}

export interface NotificationResult {
  success: boolean;
  message: string;
}

export interface SellerOrderPaidPayload {
  notification_type: NotificationType.SELLER_ORDER_PAID;
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  price?: number;
}

export interface BuyerGiftSentPayload {
  notification_type: NotificationType.BUYER_GIFT_SENT;
  orderId: string;
  buyerId: string;
  orderNumber?: number;
}

export interface SellerNewProposalPayload {
  notification_type: NotificationType.SELLER_NEW_PROPOSAL;
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  proposedPrice?: number;
  originalPrice?: number;
}

export interface ProposerAcceptedPayload {
  notification_type: NotificationType.PROPOSER_ACCEPTED;
  orderId: string;
  proposerId: string;
  orderNumber?: number;
  acceptedPrice?: number;
}

export type UnifiedNotificationPayload =
  | SellerOrderPaidPayload
  | BuyerGiftSentPayload
  | SellerNewProposalPayload
  | ProposerAcceptedPayload;
