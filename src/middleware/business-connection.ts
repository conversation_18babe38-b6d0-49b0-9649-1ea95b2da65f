import { Context } from "telegraf";
import { TelegramBusinessMessageContext } from "../app.types";
import {
  handleDirectGiftDeposit,
  type DirectDepositFlowContext,
} from "../flows/direct-gift-deposit-flow";
import { handleGiftWithdrawal } from "../flows/gift-withdrawal-flow";
import { getUserSession } from "../services/session-service/session-service";
import { getGiftToTransfer } from "../utils/business-connection-helpers";
import { businessConnectionLogger } from "./business-connection.logger";

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    businessConnectionLogger.logMiddlewareStarted();

    const update = ctx.update as unknown as TelegramBusinessMessageContext;

    businessConnectionLogger.logUpdateDebug(ctx.update);

    if (!update?.business_message) {
      businessConnectionLogger.logNoBusinessMessage();
      await next();
      return;
    }

    const chat_id = update.business_message.chat.id;
    businessConnectionLogger.logProcessingBusinessMessage({ chat_id });

    const userId = update.business_message.from?.id?.toString();
    if (!userId) {
      businessConnectionLogger.logNoUserIdFound({ chat_id });
      await next();
      return;
    }

    businessConnectionLogger.logUserIdExtracted({ chat_id, userId });

    const session = await getUserSession(userId);
    businessConnectionLogger.logUserSessionRetrieved({
      chat_id,
      userId,
      hasSession: !!session,
      pendingOrderId: session?.pendingOrderId,
      withdrawalGiftId: session?.withdrawal_gift_id,
    });

    // TODO refactor with DRY
    const withdrawalGiftId = session?.withdrawal_gift_id;
    const giftToTransfer = getGiftToTransfer(ctx);

    // Check if user has a withdrawal gift ID and is not sending a gift (withdrawal flow)
    if (withdrawalGiftId && !giftToTransfer) {
      businessConnectionLogger.logGiftWithdrawalDetected({
        chat_id,
        userId,
        withdrawalGiftId,
      });

      const success = await handleGiftWithdrawal({
        ctx,
        chat_id,
        userId,
        withdrawalGiftId,
      });

      if (success) {
        businessConnectionLogger.logGiftWithdrawalSuccess({
          chat_id,
          userId,
          withdrawalGiftId,
        });
      } else {
        businessConnectionLogger.logGiftWithdrawalFailed({
          chat_id,
          userId,
          withdrawalGiftId,
        });
      }

      await next();
      return;
    }

    // Check if user is depositing a gift without an order (direct deposit flow)
    if (giftToTransfer) {
      businessConnectionLogger.logDirectGiftDepositDetected({
        chat_id,
        userId,
        giftToTransfer,
      });

      const directDepositContext: DirectDepositFlowContext = {
        ctx,
        chat_id,
        userId,
      };
      const success = await handleDirectGiftDeposit(
        directDepositContext,
        giftToTransfer
      );

      if (success) {
        businessConnectionLogger.logDirectGiftDepositSuccess({
          chat_id,
          userId,
        });
      } else {
        businessConnectionLogger.logDirectGiftDepositFailed({
          chat_id,
          userId,
        });
      }

      await next();
      return;
    }

    await next();
  } catch (error) {
    businessConnectionLogger.logMiddlewareError(error);
    await next();
  }
};
