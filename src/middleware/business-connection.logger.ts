import { LogOperations } from "../app.constants";
import { log } from "../utils/logger";

interface BaseBusinessConnectionParams {
  chat_id: string | number;
  userId: string;
}

interface BusinessConnectionWithOrderParams
  extends BaseBusinessConnectionParams {
  pendingOrderId: string;
}

interface BusinessConnectionWithGiftParams
  extends BaseBusinessConnectionParams {
  withdrawalGiftId: string;
}

export const businessConnectionLogger = {
  // Middleware lifecycle logging
  logMiddlewareStarted() {
    log.info("Business connection middleware started", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE_START,
    });
  },

  logMiddlewareCompleted(params: BusinessConnectionWithOrderParams) {
    log.info("Business connection middleware completed", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE_END,
      ...params,
    });
  },

  logMiddlewareError(error: unknown) {
    log.error("Error handling update", error, {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
    });
  },

  // Business message processing
  logNoBusinessMessage() {
    log.info("No business message found, skipping middleware", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
    });
  },

  logProcessingBusinessMessage(params: { chat_id: string | number }) {
    log.info("Processing business message", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logNoUserIdFound(params: { chat_id: string | number }) {
    log.warn("No user ID found in business message", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logUserIdExtracted(params: BaseBusinessConnectionParams) {
    log.info("User ID extracted from business message", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  // Session management
  logUserSessionRetrieved(
    params: BaseBusinessConnectionParams & {
      hasSession: boolean;
      pendingOrderId?: string | undefined;
      withdrawalGiftId?: string | undefined;
    }
  ) {
    log.info("User session retrieved", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  // Gift withdrawal flow
  logGiftWithdrawalDetected(params: BusinessConnectionWithGiftParams) {
    log.info("Gift withdrawal detected", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logGiftWithdrawalSuccess(params: BusinessConnectionWithGiftParams) {
    log.info("Gift withdrawal completed successfully", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logGiftWithdrawalFailed(params: BusinessConnectionWithGiftParams) {
    log.warn("Gift withdrawal failed", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  // Direct gift deposit flow
  logDirectGiftDepositDetected(
    params: BaseBusinessConnectionParams & {
      giftToTransfer: any;
    }
  ) {
    log.info("Direct gift deposit detected (no pending order)", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logDirectGiftDepositSuccess(params: BaseBusinessConnectionParams) {
    log.info("Direct gift deposit completed successfully", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logDirectGiftDepositFailed(params: BaseBusinessConnectionParams) {
    log.warn("Direct gift deposit failed", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  // Order processing
  logNoPendingOrderFound(params: BaseBusinessConnectionParams) {
    log.warn("No pending order found for user", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logGiftExtractionResult(
    params: BusinessConnectionWithOrderParams & {
      giftIdToTransfer: any;
    }
  ) {
    log.info("Gift ID extraction result", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  logNoGiftToTransferCheck(params: BusinessConnectionWithOrderParams) {
    log.info(
      "No gift to transfer, checking for buyer gift request or cancelled gift retrieval",
      {
        operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
        ...params,
      }
    );
  },

  logNoApplicableFlowFound(params: BusinessConnectionWithOrderParams) {
    log.warn("No applicable flow found for user request", {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      ...params,
    });
  },

  // Debug logging (for development)
  logUpdateDebug(update: any) {
    // Only log in development to avoid cluttering production logs
    if (process.env.NODE_ENV === "development") {
      log.debug("Business connection update debug", {
        operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
        update: JSON.stringify(update),
      });
    }
  },
};
