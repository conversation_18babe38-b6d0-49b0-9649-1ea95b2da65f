import { Context } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../utils/keyboards";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { APP_NAME, PREM_RELAYER_USERNAME } from "../app.constants";
import { HealthcheckService } from "../services/healthcheck-service/healthcheck-service";
import { HandlersLogger } from "./handlers.logger";

export const handleStartCommand = (ctx: Context) => {
  ctx.reply(
    T(ctx, botMessages.welcome.id, { APP_NAME }),
    createMainKeyboard(ctx)
  );
};

export const handleHelpCommand = (ctx: Context) => {
  ctx.reply(
    T(ctx, botMessages.help.id, {
      APP_NAME,
      relayerUsername: PREM_RELAYER_USERNAME,
    }),
    createMarketplaceInlineKeyboard(ctx)
  );
};

export const handleHealthCommand = async (ctx: Context) => {
  try {
    const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
    const isHealthy = await HealthcheckService.isHealthy();

    if (lastHealthcheck) {
      const healthStatus = isHealthy
        ? botMessages.healthStatusHealthy.defaultMessage
        : botMessages.healthStatusUnhealthy.defaultMessage;
      const message = T(ctx, botMessages.healthLastCheck.id, {
        status: healthStatus,
        timestamp: lastHealthcheck,
      });
      ctx.reply(message);
    } else {
      ctx.reply(T(ctx, botMessages.healthNoData.id));
    }
  } catch (error) {
    HandlersLogger.logHealthCommandError({
      error,
      chatId: String(ctx.chat?.id),
      userId: String(ctx.from?.id),
    });
    ctx.reply(T(ctx, botMessages.healthError.id));
  }
};
