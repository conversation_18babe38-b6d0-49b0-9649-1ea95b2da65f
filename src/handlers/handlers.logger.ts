import { LogOperations } from "../app.constants";
import { log } from "../utils/logger";

export const HandlersLogger = {
  logUserBuyOrdersError(params: {
    error: unknown;
    userId: string;
    chatId: string;
  }) {
    log.error("Error fetching user buy orders", params.error, {
      operation: LogOperations.BOT_OPERATION,
      component: "handle_get_my_buy_orders_button",
      userId: params.userId,
      chatId: params.chatId,
    });
  },

  logUserSellOrdersError(params: {
    error: unknown;
    userId: string;
    chatId: string;
  }) {
    log.error("Error fetching user sell orders", params.error, {
      operation: LogOperations.BOT_OPERATION,
      component: "handle_get_my_sell_orders_button",
      userId: params.userId,
      chatId: params.chatId,
    });
  },

  logHealthCommandError(params: {
    error: unknown;
    chatId: string;
    userId: string;
  }) {
    log.error("Error in health command", params.error, {
      operation: LogOperations.HEALTH_COMMAND,
      chatId: params.chatId,
      userId: params.userId,
    });
  },

  logGetMyGiftsButtonError(params: {
    error: unknown;
    userId: string;
    chatId: string;
  }) {
    log.error("Error in handleGetMyGiftsButton", {
      operation: LogOperations.GET_MY_GIFTS_BUTTON,
      error: params.error,
      userId: params.userId,
      chatId: params.chatId,
    });
  },

  logClearSessionOnDepositWarning(params: { tgId: string; error: string }) {
    log.warn("Failed to clear user session on deposit gift", {
      operation: LogOperations.DEPOSIT_GIFT_CLEAR_SESSION,
      tgId: params.tgId,
      error: params.error,
    });
  },
};
