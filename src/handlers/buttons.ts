import { Context, Markup } from "telegraf";
import { PREM_RELAYER_USERNAME, PREM_SUPPORT_OFFICIAL } from "../app.constants";
import { T } from "../i18n";
import { buttonsMessages } from "../intl/buttons.messages";
import { botMessages } from "../intl/messages";
import { getUserGiftsAvailableForWithdrawal } from "../services/gift-service/gift-query.service";
import { clearUserSession } from "../services/session-service/session-service";
import {
  getSimulationService,
  isSimulationEnabled,
} from "../services/simulation";
import { createMarketplaceInlineKeyboard } from "../utils/keyboards";
import { HandlersLogger } from "./handlers.logger";

export const handleGetMyGiftsButton = async (ctx: Context) => {
  try {
    const tg_id = ctx.from?.id?.toString();
    if (!tg_id) {
      ctx.reply(T(ctx, botMessages.telegramIdError.id));
      return;
    }

    ctx.reply(T(ctx, botMessages.fetchingGifts.id));

    const gifts = await getUserGiftsAvailableForWithdrawal({ tg_id });

    if (!gifts || gifts.length === 0) {
      ctx.reply(
        T(ctx, botMessages.noGiftsAvailable.id, {
          relayerUsername: PREM_RELAYER_USERNAME,
        }),
        createMarketplaceInlineKeyboard(ctx)
      );
      return;
    }

    let message =
      T(ctx, botMessages.giftsAvailableForWithdrawal.id, {
        count: gifts.length,
      }) + "\n\n";

    gifts.forEach((gift, index) => {
      const baseName = gift.base_name || T(ctx, botMessages.unknownGift.id);
      const modelName = gift.model?.name ? `(${gift.model.name})` : "";
      const giftName = `${baseName} ${modelName}`.trim();
      const orderInfo = gift.relatedOrder
        ? T(ctx, botMessages.linkedOrder.id, {
            orderNumber: gift.relatedOrder.number,
            orderStatus: gift.relatedOrder.status,
          })
        : T(ctx, botMessages.noLinkedOrder.id);

      message += `${index + 1}. ${giftName}${orderInfo}\n\n`;
    });

    message += T(ctx, botMessages.withdrawInstructions.id);

    // Create buttons for gift withdrawal (limit to first 10 gifts)
    const giftButtons = gifts.slice(0, 10).map((gift) => {
      const giftName = gift.base_name || T(ctx, botMessages.unknownGift.id);
      const modelName = gift.model?.name ? `(${gift.model.name})` : "";

      const buttonText = T(ctx, botMessages.withdrawGift.id, {
        giftName: `${giftName} ${modelName}`,
      }).trim();

      return [Markup.button.callback(buttonText, `withdraw_gift_${gift.id}`)];
    });

    giftButtons.push([
      Markup.button.callback(
        T(ctx, botMessages.openMarketplace.id),
        "open_marketplace"
      ),
    ]);

    if (gifts.length > 10) {
      message += `\n\n⚠️ Showing first 10 gifts. You have ${
        gifts.length - 10
      } more gifts available.`;
    }

    ctx.reply(message, Markup.inlineKeyboard(giftButtons));
  } catch (error) {
    HandlersLogger.logGetMyGiftsButtonError({
      error,
      userId: String(ctx.from?.id),
      chatId: String(ctx.chat?.id),
    });
    ctx.reply(
      T(ctx, botMessages.fetchError.id),
      createMarketplaceInlineKeyboard(ctx)
    );
  }
};

export const handleContactSupportButton = (ctx: Context) => {
  ctx.reply(
    T(ctx, botMessages.supportContactInfo.id, {
      PREM_SUPPORT_OFFICIAL,
    }),
    createMarketplaceInlineKeyboard(ctx)
  );
};

export const handleDepositGiftButton = async (ctx: Context) => {
  const tgId = ctx.from?.id?.toString();

  if (!tgId) {
    ctx.reply(T(ctx, botMessages.telegramIdError.id));
    return;
  }

  // Clear user session as it's no longer relevant after deposit operation
  try {
    await clearUserSession(tgId);
  } catch (error) {
    HandlersLogger.logClearSessionOnDepositWarning({
      tgId,
      error: error instanceof Error ? error.message : String(error),
    });
    // Continue with deposit operation even if session clear fails
  }

  if (isSimulationEnabled()) {
    const simulationService = getSimulationService();
    await simulationService.handleGiftDepositSimulation({
      ctx,
      tgId,
    });
  } else {
    const message = T(ctx, buttonsMessages.depositGiftInstructions.id, {
      premRelayerUsername: PREM_RELAYER_USERNAME,
    });

    ctx.reply(message, createMarketplaceInlineKeyboard(ctx));
  }
};
