'use client';

import { openTelegramLink, shareURL } from '@telegram-apps/sdk-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { commonMessages } from '@/intl/common.messages';
import { generateOrderShareLink } from '@/utils/order-deep-link-utils';

import { useShareLinkMessages } from './intl/use-share-link.messages';

export interface UseShareLinkOptions {
  orderId?: string;
}

export interface UseShareLinkReturn {
  handleShare: () => Promise<void>;
  isSharing: boolean;
}

export const useShareLink = ({
  orderId,
}: UseShareLinkOptions): UseShareLinkReturn => {
  const [isSharing, setIsSharing] = useState(false);
  const { formatMessage } = useIntl();

  const handleShare = async () => {
    setIsSharing(true);

    if (!orderId) {
      toast.error(formatMessage(useShareLinkMessages.orderIdNotAvailable));
      setIsSharing(false);
      return;
    }

    try {
      const shareLink = generateOrderShareLink(orderId);

      if (shareURL.isAvailable()) {
        shareURL(shareLink, formatMessage(useShareLinkMessages.checkOutOrder));
      } else if (openTelegramLink.isAvailable()) {
        openTelegramLink(shareLink);
      } else {
        // Copy to clipboard instead of opening in new window
        await navigator.clipboard.writeText(shareLink);
        toast.success(formatMessage(commonMessages.linkCopiedToClipboard));
      }
    } catch (error) {
      console.error('Error sharing order:', error);

      // Fallback to clipboard copy
      try {
        const shareLink = generateOrderShareLink(orderId);
        await navigator.clipboard.writeText(shareLink);
        toast.success(formatMessage(commonMessages.linkCopiedToClipboard));
      } catch (clipboardError) {
        console.error('Error copying to clipboard:', clipboardError);
        toast.error(formatMessage(useShareLinkMessages.failedToShare));
      }
    } finally {
      setIsSharing(false);
    }
  };

  return {
    handleShare,
    isSharing,
  };
};
