'use client';

import { useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';

export type Theme = 'default' | 'dark' | 'black';

const THEME_STORAGE_KEY = 'marketplace-theme';

export function useThemeSwitcher() {
  const [storedTheme, setStoredTheme] = useLocalStorage<Theme>(
    THEME_STORAGE_KEY,
    'dark', // Default to dark theme as it was the original
  );
  const [theme, setTheme] = useState<Theme>(storedTheme);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;

    // Remove all theme classes
    root.classList.remove('dark', 'black');

    // Add the current theme class (except for default)
    if (theme !== 'default') {
      root.classList.add(theme);
    }

    // Update stored theme
    setStoredTheme(theme);
  }, [theme, setStoredTheme]);

  // Initialize theme on mount
  useEffect(() => {
    setTheme(storedTheme);
  }, [storedTheme]);

  const switchTheme = (newTheme: Theme) => {
    setTheme(newTheme);
  };

  const toggleTheme = () => {
    const themes: Theme[] = ['default', 'dark', 'black'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const getThemeLabel = (themeValue: Theme): string => {
    switch (themeValue) {
      case 'default':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'black':
        return 'Black';
      default:
        return 'Unknown';
    }
  };

  const getThemeIcon = (themeValue: Theme): string => {
    switch (themeValue) {
      case 'default':
        return '☀️';
      case 'dark':
        return '🌙';
      case 'black':
        return '⚫';
      default:
        return '❓';
    }
  };

  return {
    theme,
    switchTheme,
    toggleTheme,
    getThemeLabel,
    getThemeIcon,
    isDefault: theme === 'default',
    isDark: theme === 'dark',
    isBlack: theme === 'black',
  };
}
