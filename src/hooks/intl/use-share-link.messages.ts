import { defineMessages } from 'react-intl';

export const useShareLinkMessages = defineMessages({
  orderIdNotAvailable: {
    id: 'shareLink.orderIdNotAvailable',
    defaultMessage: 'Order ID not available',
  },
  checkOutOrder: {
    id: 'shareLink.checkOutOrder',
    defaultMessage: 'Check out this order!',
  },

  failedToShare: {
    id: 'shareLink.failedToShare',
    defaultMessage: 'Failed to share order',
  },
});
