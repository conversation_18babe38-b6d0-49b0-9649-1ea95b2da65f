import { defineMessages } from 'react-intl';

export const purchaseMessages = defineMessages({
  // Main success message
  purchaseSuccessful: {
    id: 'purchase.successful',
    defaultMessage: 'Purchase successful!',
  },

  // Lock scenarios
  fundsLocked: {
    id: 'purchase.fundsLocked',
    defaultMessage:
      '{lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order).',
  },
  paymentCompleted: {
    id: 'purchase.paymentCompleted',
    defaultMessage:
      'Payment completed! {netAmountToSeller} TON transferred to seller.',
  },

  // Fee messages
  purchaseFeeApplied: {
    id: 'purchase.feeApplied',
    defaultMessage: 'Purchase fee of {totalFee} TON applied.',
  },

  // Action messages for buyers
  buyerGiftReadyToClaim: {
    id: 'purchase.buyer.giftReadyToClaim',
    defaultMessage: 'Gift is ready to be claimed from relayer.',
  },
  buyerWaitingForSeller: {
    id: 'purchase.buyer.waitingForSeller',
    defaultMessage: 'Waiting for seller to send gift.',
  },

  // Action messages for sellers
  sellerCanSendGift: {
    id: 'purchase.seller.canSendGift',
    defaultMessage: 'You can now send the gift.',
  },

  // Complete formatted messages for different scenarios
  buyerPurchaseWithLockAndFee: {
    id: 'purchase.buyer.withLockAndFee',
    defaultMessage:
      'Purchase successful! {lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order). Purchase fee of {totalFee} TON applied. {actionMessage}',
  },
  buyerPurchaseWithLockNoFee: {
    id: 'purchase.buyer.withLockNoFee',
    defaultMessage:
      'Purchase successful! {lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order). {actionMessage}',
  },
  buyerPurchasePaymentCompletedWithFee: {
    id: 'purchase.buyer.paymentCompletedWithFee',
    defaultMessage:
      'Purchase successful! Payment completed! {netAmountToSeller} TON transferred to seller. Purchase fee of {totalFee} TON applied. {actionMessage}',
  },
  buyerPurchasePaymentCompletedNoFee: {
    id: 'purchase.buyer.paymentCompletedNoFee',
    defaultMessage:
      'Purchase successful! Payment completed! {netAmountToSeller} TON transferred to seller. {actionMessage}',
  },
  sellerPurchaseWithLock: {
    id: 'purchase.seller.withLock',
    defaultMessage:
      'Purchase successful! {lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order). {actionMessage}',
  },
});
