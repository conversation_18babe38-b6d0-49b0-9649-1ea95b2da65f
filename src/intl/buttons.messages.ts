import { defineMessages } from "@formatjs/intl";

export const buttonsMessages = defineMessages({
  cancel: {
    id: "buttons.cancel",
    defaultMessage: "❌ Cancel",
  },
  contactSupport: {
    id: "buttons.contactSupport",
    defaultMessage: "📞 Contact Support",
  },
  depositGift: {
    id: "buttons.depositGift",
    defaultMessage: "📦 Deposit Gift",
  },
  myGifts: {
    id: "buttons.myGifts",
    defaultMessage: "🎁 My Gifts",
  },
  openMarketplace: {
    id: "buttons.openMarketplace",
    defaultMessage: "🌐 Open Marketplace",
  },
  withdrawGift: {
    id: "buttons.withdrawGift",
    defaultMessage: "Withdraw {giftName}",
  },
  depositGiftInstructions: {
    id: "buttons.depositGiftInstructions",
    defaultMessage:
      "🎁 Deposit a Gift\n\nYou should go to {premRelayerUsername} and just deposit a gift. Then, you will see your deposited gift in the Pram app in the tab called 'My Gifts.'\n\nSteps:\n1. Go to {premRelayerUsername}\n2. Send your gift to the relayer\n3. Your gift will appear in the Pram app under 'My Gifts'\n\nThis allows you to deposit gifts without linking them to a specific order.",
  },
});
