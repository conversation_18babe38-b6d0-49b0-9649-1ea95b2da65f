'use client';

import { ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import type { OrderGift } from '@/mikerudenko/marketplace-shared';

import { orderTraitsSectionMessages } from './intl/order-traits-section.messages';

interface OrderTraitsSectionProps {
  gift: OrderGift;
}

interface TraitRowProps {
  name: string;
  value: string;
  rarity: string;
}

function TraitRow({ name, value, rarity }: TraitRowProps) {
  return (
    <div className="flex justify-between items-center py-2">
      <span className="text-muted-foreground text-sm capitalize">{name}</span>
      <span className="text-foreground text-sm font-medium">
        {value} <span className="text-primary">({rarity})</span>
      </span>
    </div>
  );
}

export function OrderTraitsSection({ gift }: OrderTraitsSectionProps) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  const formatRarity = (rarityPerMille: number): string => {
    const percentage = (rarityPerMille / 10).toFixed(1);
    return `${percentage}%`;
  };

  const traits = [
    {
      name: 'backdrop',
      value: gift.backdrop.name,
      rarity: formatRarity(gift.backdrop.rarity_per_mille),
    },
    {
      name: 'model',
      value: gift.model.name,
      rarity: formatRarity(gift.model.rarity_per_mille),
    },
    {
      name: 'symbol',
      value: gift.symbol.name,
      rarity: formatRarity(gift.symbol.rarity_per_mille),
    },
  ];

  return (
    <div className="space-y-4">
      <Collapsible defaultOpen={false} open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-background/50 rounded-lg hover:bg-background/70 transition-colors duration-200">
          <span className="text-foreground font-medium">
            {t(orderTraitsSectionMessages.giftTraits)}
          </span>
          <ChevronDown
            className={cn(
              'h-4 w-4 text-muted-foreground transition-transform duration-300 ease-in-out',
              isOpen && 'rotate-180',
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
          <div className="px-4 pb-4 space-y-4 bg-background/50 rounded-lg">
            <div className="bg-card border border-border rounded-lg p-4">
              <div className="space-y-1">
                {traits.map((trait) => (
                  <TraitRow
                    key={trait.name}
                    name={trait.name}
                    value={trait.value}
                    rarity={trait.rarity}
                  />
                ))}
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
