import { useIntl } from 'react-intl';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

import { secondaryMarketBadgeMessages } from './secondary-market-badge/intl/secondary-market-badge.messages';

interface SecondaryMarketBadgeProps {
  className?: string;
}

export function SecondaryMarketBadge({ className }: SecondaryMarketBadgeProps) {
  const { formatMessage: t } = useIntl();

  return (
    <Badge
      variant="secondary"
      className={cn(
        'bg-[var(--color-status-blue-bg)] text-[var(--color-status-blue)] border-[var(--color-status-blue-border)] text-[10px]',
        className,
      )}
    >
      {t(secondaryMarketBadgeMessages.resell)}
    </Badge>
  );
}
