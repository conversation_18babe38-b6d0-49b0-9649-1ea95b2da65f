'use client';

import { Button } from '@/components/ui/button';
import { type Theme, useThemeSwitcher } from '@/hooks/use-theme-switcher';
import { cn } from '@/lib/utils';

interface ThemeSwitcherProps {
  className?: string;
  variant?: 'button' | 'toggle' | 'dropdown';
}

export function ThemeSwitcher({
  className,
  variant = 'toggle',
}: ThemeSwitcherProps) {
  const { theme, switchTheme, toggleTheme, getThemeLabel, getThemeIcon } =
    useThemeSwitcher();

  if (variant === 'toggle') {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleTheme}
        className={cn(
          'flex items-center gap-2 text-foreground hover:bg-accent',
          className,
        )}
        title={`Current theme: ${getThemeLabel(theme)}. Click to switch.`}
      >
        <span className="text-lg">{getThemeIcon(theme)}</span>
        <span className="hidden sm:inline">{getThemeLabel(theme)}</span>
      </Button>
    );
  }

  if (variant === 'dropdown') {
    const themes: Theme[] = ['default', 'dark', 'black'];

    return (
      <div className={cn('flex gap-1 p-1 bg-muted rounded-lg', className)}>
        {themes.map((themeOption) => (
          <Button
            key={themeOption}
            variant={theme === themeOption ? 'default' : 'ghost'}
            size="sm"
            onClick={() => switchTheme(themeOption)}
            className={cn(
              'flex items-center gap-1 text-xs',
              theme === themeOption && 'bg-primary text-primary-foreground',
            )}
          >
            <span>{getThemeIcon(themeOption)}</span>
            <span className="hidden sm:inline">
              {getThemeLabel(themeOption)}
            </span>
          </Button>
        ))}
      </div>
    );
  }

  // Button variant - just the current theme
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleTheme}
      className={cn('flex items-center gap-2', className)}
    >
      <span>{getThemeIcon(theme)}</span>
      <span>{getThemeLabel(theme)}</span>
    </Button>
  );
}

// Individual theme buttons for more control
interface ThemeButtonProps {
  theme: Theme;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
}

export function ThemeButton({
  theme,
  isActive,
  onClick,
  className,
}: ThemeButtonProps) {
  const { getThemeLabel, getThemeIcon } = useThemeSwitcher();

  return (
    <Button
      variant={isActive ? 'default' : 'ghost'}
      size="sm"
      onClick={onClick}
      className={cn(
        'flex items-center gap-2',
        isActive && 'bg-primary text-primary-foreground',
        className,
      )}
    >
      <span>{getThemeIcon(theme)}</span>
      <span>{getThemeLabel(theme)}</span>
    </Button>
  );
}

// Compact theme switcher for mobile
export function CompactThemeSwitcher({ className }: { className?: string }) {
  const { theme, toggleTheme, getThemeIcon } = useThemeSwitcher();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className={cn('w-8 h-8 text-foreground hover:bg-accent', className)}
      title="Switch theme"
    >
      <span className="text-lg">{getThemeIcon(theme)}</span>
    </Button>
  );
}
