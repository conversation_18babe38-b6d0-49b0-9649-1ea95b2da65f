import { defineMessages } from 'react-intl';

export const orderActorsMessages = defineMessages({
  orderActors: {
    id: 'orderActors.orderActors',
    defaultMessage: 'Order Actors',
  },
  anonymousUser: {
    id: 'orderActors.anonymousUser',
    defaultMessage: 'Anonymous User',
  },
  buyer: {
    id: 'orderActors.buyer',
    defaultMessage: 'Buyer',
  },
  seller: {
    id: 'orderActors.seller',
    defaultMessage: 'Seller',
  },
  resseller: {
    id: 'orderActors.resseller',
    defaultMessage: 'Resseller',
  },
  noBuyerAssigned: {
    id: 'orderActors.noBuyerAssigned',
    defaultMessage: 'No buyer assigned',
  },
  noSellerAssigned: {
    id: 'orderActors.noSellerAssigned',
    defaultMessage: 'No seller assigned',
  },
  noRoleAssigned: {
    id: 'orderActors.noRoleAssigned',
    defaultMessage: 'No {role} assigned',
  },
});
