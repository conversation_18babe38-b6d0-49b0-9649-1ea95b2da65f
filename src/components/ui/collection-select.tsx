'use client';

import { Check, ChevronDown, Search } from 'lucide-react';
import React, { useCallback, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';
import { Drawer } from 'vaul';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { GridItem, ItemCacheProvider } from '@/components/ui/virtualized-grid';
import { useVisualViewport } from '@/hooks/use-visual-viewport';
import { cn } from '@/lib/utils';
import {
  type CollectionEntity,
  CollectionStatus,
  UserType,
} from '@/mikerudenko/marketplace-shared';
import { useCollectionStatusText } from '@/services/collection-status-service';

import { TgsOrImage } from '../tgs/tgs-or-image';
import { collectionSelectMessages } from './collection-select/intl/collection-select.messages';

const tguiClasses =
  'tgui-c3e2e598bd70eee6 tgui-080a44e6ac3f4d27 tgui-809f1f8a3f64154d tgui-266b6ffdbad2b90e tgui-8f63cd31b2513281 tgui-9f9a52f695b85cc9';

interface CollectionSelectProps {
  animated?: boolean;
  collections: CollectionEntity[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  hideLabel?: boolean;
  showAllOption?: boolean;
  userType?: UserType;
}

const CollectionSelectComponent = function CollectionSelect({
  animated,
  collections,
  value,
  onValueChange,
  placeholder = 'Select collection...',
  className,
  hideLabel,
  showAllOption = true,
  userType,
}: CollectionSelectProps) {
  const { formatMessage: t } = useIntl();
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const drawerContentRef = useVisualViewport({ enabled: open, offset: 64 });
  const getCollectionStatusText = useCollectionStatusText();

  const filteredCollections = useMemo(() => {
    let activeCollections = collections.filter(
      (collection) => collection.active !== false,
    );

    // Filter out MARKET collections for buyers
    if (userType === UserType.BUYER) {
      activeCollections = activeCollections.filter(
        (collection) => collection.status !== CollectionStatus.MARKET,
      );
    }

    if (!searchQuery) return activeCollections;
    return activeCollections.filter((collection) =>
      collection.name.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [collections, searchQuery, userType]);

  const selectedCollection = useMemo(
    () => collections.find((collection) => collection.id === value),
    [collections, value],
  );

  const handleSelect = useCallback(
    (collectionId: string) => {
      onValueChange(collectionId);
      setOpen(false);
      setSearchQuery('');
    },
    [onValueChange],
  );

  const handleOpenChange = useCallback((newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchQuery('');
    }
  }, []);

  return (
    <>
      <Button
        variant="outline"
        aria-expanded={open}
        onClick={() => setOpen(true)}
        className={cn(
          'w-full justify-between h-9 px-3 py-2 text-sm bg-transparent! border-none text-[var(--tgui--secondary_hint_color)]',
          'hover:bg-[var(--tgui--bg_color)] hover:text-[var(--tgui--secondary_hint_color)]',
          open && 'text-[var(--ring)] shadow-[0_0_0_2px_var(--ring)]!',
          'rounded-[14px] shadow-[0_0_0_2px_var(--tgui--outline)] relative',
          className,
        )}
      >
        {!hideLabel && (
          <h6
            className={cn(
              'px-[6px] bg-[var(--tgui--bg_color)] absolute -top-3 left-7',
              tguiClasses,
            )}
          >
            {t(collectionSelectMessages.collection)}
          </h6>
        )}

        {selectedCollection ? (
          <div className="flex items-center gap-2 min-w-0">
            <div className="relative w-6 h-6 rounded-sm overflow-hidden bg-muted flex-shrink-0">
              <TgsOrImage
                isImage={true}
                collectionId={selectedCollection.id}
                imageProps={{
                  alt: selectedCollection.name,
                  fill: true,
                  className: 'object-cover',
                }}
              />
            </div>
            <span
              className={cn(
                tguiClasses,
                '!font-[var(--tgui--font_weight--accent3)] truncate text-white',
              )}
            >
              {selectedCollection.name}
            </span>
          </div>
        ) : (
          <span
            className={cn(
              tguiClasses,
              '!font-[var(--tgui--font_weight--accent3)] truncate text-white',
            )}
          >
            {placeholder}
          </span>
        )}
        <ChevronDown
          className={cn(
            'ml-2 h-4 w-4 shrink-0 opacity-50 text-muted-foreground transition-transform',
            open && 'rotate-180',
          )}
        />
      </Button>

      <Drawer.Root
        open={open}
        onOpenChange={handleOpenChange}
        shouldScaleBackground
        modal={true}
        dismissible={true}
      >
        <Drawer.Portal>
          <Drawer.Title />
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
          <Drawer.Content
            ref={drawerContentRef}
            className="bg-card flex flex-col rounded-t-[10px] mt-24 max-h-[90vh] fixed bottom-0 left-0 right-0 z-[101] border-t border-border outline-none focus:outline-none"
          >
            <div className="p-4 bg-card rounded-t-[10px] flex-1 overflow-y-auto">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-muted-foreground mb-8 cursor-grab active:cursor-grabbing touch-manipulation" />

              <div className="max-w-md mx-auto pb-8 px-2 w-full">
                <Drawer.Title className="font-medium mb-4 text-lg text-foreground">
                  Select Collection
                </Drawer.Title>

                <div className="flex flex-col space-y-4 min-h-0">
                  <div className="flex items-center border border-border rounded-lg px-3 py-2 bg-background flex-shrink-0">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50 text-muted-foreground" />
                    <Input
                      placeholder={t(
                        collectionSelectMessages.searchCollections,
                      )}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 h-8 bg-transparent text-foreground placeholder:text-muted-foreground"
                    />
                  </div>

                  <div className="overflow-y-auto scrollbar-thin">
                    <ItemCacheProvider>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {showAllOption && (
                          <div
                            className={cn(
                              'relative flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all duration-200 border min-h-[120px] group',
                              value === '' || value === 'all'
                                ? 'bg-card border-primary'
                                : 'bg-card border-border hover:bg-card/80 hover:border-primary/50',
                            )}
                            onClick={() => handleSelect('')}
                          >
                            <div className="w-full aspect-square rounded-lg bg-background flex items-center justify-center mb-2">
                              <span className="text-lg text-foreground font-bold">
                                All
                              </span>
                            </div>
                            <span className="text-xs text-center text-foreground font-medium leading-tight truncate w-full">
                              All Collections
                            </span>
                            {(value === '' || value === 'all') && (
                              <div className="absolute top-2 right-2">
                                <Check className="h-4 w-4 text-primary" />
                              </div>
                            )}
                          </div>
                        )}

                        {filteredCollections.length === 0 && searchQuery ? (
                          <div className="col-span-full py-8 text-center text-sm text-muted-foreground">
                            {t(collectionSelectMessages.noCollectionsFound, {
                              searchQuery,
                            })}
                          </div>
                        ) : (
                          filteredCollections.map((collection, index) => (
                            <GridItem
                              key={collection.id}
                              itemId={`collection-${collection.id}`}
                              index={index}
                              initialRenderedCount={12}
                            >
                              <div
                                className={cn(
                                  'relative flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all duration-150 border min-h-[120px] group',
                                  'will-change-transform transform-gpu',
                                  value === collection.id
                                    ? 'bg-input border-primary'
                                    : 'bg-card border-border hover:bg-input hover:border-primary/50',
                                )}
                                onClick={() => handleSelect(collection.id)}
                              >
                                {/* Status Label */}
                                <div className="absolute top-2 left-2 z-10">
                                  <span
                                    className={cn(
                                      'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                                      collection.status ===
                                        CollectionStatus.MARKET
                                        ? 'bg-[var(--color-status-green-bg)] text-[var(--color-status-green)] border border-[var(--color-status-green-border)]'
                                        : 'bg-[var(--color-status-yellow-bg)] text-[var(--color-status-yellow)] border border-[var(--color-status-yellow-border)]',
                                    )}
                                  >
                                    {getCollectionStatusText(collection.status)}
                                  </span>
                                </div>

                                <div className="w-full aspect-square relative rounded-lg overflow-hidden bg-background mb-2">
                                  <TgsOrImage
                                    isImage={!animated}
                                    collectionId={collection.id}
                                    imageProps={{
                                      alt: collection.name,
                                      fill: true,
                                      className:
                                        'object-cover group-hover:scale-105 transition-transform duration-200 p-4',
                                      loading: 'lazy',
                                      sizes:
                                        '(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw',
                                    }}
                                    tgsProps={{
                                      style: {
                                        height: 'auto',
                                        width: 'auto',
                                        padding: '16px',
                                      },
                                    }}
                                  />
                                </div>
                                <span className="text-xs text-center text-foreground font-medium leading-tight truncate w-full">
                                  {collection.name}
                                </span>
                                {value === collection.id && (
                                  <div className="absolute top-2 right-2">
                                    <Check className="h-4 w-4 text-primary" />
                                  </div>
                                )}
                              </div>
                            </GridItem>
                          ))
                        )}
                      </div>
                    </ItemCacheProvider>
                  </div>
                </div>
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    </>
  );
};

export const CollectionSelect = React.memo(CollectionSelectComponent);
