'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { Plus } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';

interface InsufficientBalanceMessageProps {
  message: string;
  onTopUp: () => void;
  className?: string;
}

export function InsufficientBalanceMessage({
  message,
  onTopUp,
  className = '',
}: InsufficientBalanceMessageProps) {
  const { formatMessage: t } = useIntl();

  return (
    <div className={`text-center space-y-3 ${className}`}>
      <Caption level="2" weight="3" className="text-[var(--color-status-red)]">
        {message}
      </Caption>
      <Button
        onClick={onTopUp}
        size="sm"
        className="gap-2 bg-primary hover:bg-primary/90 text-primary-foreground border-0"
      >
        <Plus className="w-4 h-4" />
        {t({
          id: 'insufficientBalance.topUp',
          defaultMessage: 'Top up balance',
        })}
      </Button>
    </div>
  );
}
