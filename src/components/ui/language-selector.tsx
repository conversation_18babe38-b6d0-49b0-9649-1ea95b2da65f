'use client';

import { Globe } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { AppLocale } from '@/core.constants';
import { useRootContext } from '@/root-context';
import {
  getAvailableLanguages,
  getUserCountry,
  LANGUAGE_LABELS,
} from '@/utils/intl-utils';

export const LanguageSelector = () => {
  const { locale, setLocale } = useRootContext();
  const [userCountry] = useState(() => getUserCountry());
  const availableLanguages = getAvailableLanguages(userCountry);

  useEffect(() => {
    if (!availableLanguages.includes(locale)) {
      setLocale(availableLanguages[0]);
    }
  }, [availableLanguages, locale, setLocale]);

  const handleLanguageChange = (newLocale: AppLocale) => {
    setLocale(newLocale);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Globe className="h-4 w-4" />
          {LANGUAGE_LABELS[locale]}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {availableLanguages.map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => handleLanguageChange(lang)}
            className={locale === lang ? 'bg-accent' : ''}
          >
            {LANGUAGE_LABELS[lang]}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
