import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import type { CollectionEntity } from '@/mikerudenko/marketplace-shared';

interface OrderImageProps {
  collection: CollectionEntity | null;
  className?: string;
  aspectRatio?: string;
  showHoverEffect?: boolean;
  isAnimated?: boolean;
}

export function OrderImage({
  isAnimated,
  collection,
  className = '',
  aspectRatio = 'aspect-[1/1.2]',
  showHoverEffect = true,
}: OrderImageProps) {
  const hoverClass = showHoverEffect
    ? 'group-hover:scale-105 transition-transform duration-200'
    : '';

  return (
    <div
      className={`pt-6 relative rounded-lg overflow-hidden bg-background ${aspectRatio} ${className}`}
    >
      {collection ? (
        <TgsOrImage
          isImage={!isAnimated}
          collectionId={collection.id}
          imageProps={{
            alt: collection.name || 'Order item',
            fill: true,
            className: `object-cover p-4 mt-2 ${hoverClass}`,
          }}
          tgsProps={{
            style: { height: 'auto', width: 'auto', padding: '8px' },
          }}
        />
      ) : (
        <div className="w-full h-full bg-border rounded flex items-center justify-center">
          <div className="w-6 h-6 bg-background rounded" />
        </div>
      )}
    </div>
  );
}
