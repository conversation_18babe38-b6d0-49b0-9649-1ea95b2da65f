'use client';

import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { getGiftsAvailableForLinkingToOrder } from '@/api/gifts.api';
import { linkGiftToOrder } from '@/api/link-gift-to-order.api';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { Button } from '@/components/ui/button';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import type { GiftEntity, OrderEntity } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { attachGiftToOrderDrawerMessages } from './intl/attach-gift-to-order-drawer.messages';

interface AttachGiftToOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onGiftAttached?: () => void;
}

export function AttachGiftToOrderDrawer({
  open,
  onOpenChange,
  order,
  onGiftAttached,
}: AttachGiftToOrderDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, collections } = useRootContext();
  const [gifts, setGifts] = useState<GiftEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedGift, setSelectedGift] = useState<GiftEntity | null>(null);
  const [linking, setLinking] = useState(false);

  const loadAvailableGifts = useCallback(async () => {
    if (!order || !currentUser?.tg_id) return;

    setLoading(true);
    try {
      const availableGifts = await getGiftsAvailableForLinkingToOrder(
        currentUser.tg_id,
        order.collectionId,
      );
      setGifts(availableGifts);
    } catch (error) {
      console.error('Error loading available gifts:', error);
      toast.error(t(attachGiftToOrderDrawerMessages.errorLoadingGifts));
    } finally {
      setLoading(false);
    }
  }, [order, currentUser?.tg_id, t]);

  useEffect(() => {
    if (open && order && currentUser?.tg_id) {
      loadAvailableGifts();
    }
  }, [open, order, currentUser?.tg_id, loadAvailableGifts]);

  const handleGiftSelect = (gift: GiftEntity) => {
    setSelectedGift(gift);
  };

  const handleLinkGift = async () => {
    if (!selectedGift?.id || !order?.id || !currentUser?.id) return;

    setLinking(true);
    try {
      const result = await linkGiftToOrder(selectedGift.id, order.id);

      if (result.success) {
        toast.success(
          t(attachGiftToOrderDrawerMessages.giftLinkedSuccessfully),
        );
        onGiftAttached?.();
        onOpenChange(false);
        setSelectedGift(null);
      } else {
        toast.error(
          result.message || t(attachGiftToOrderDrawerMessages.errorLinkingGift),
        );
      }
    } catch (error) {
      console.error('Error linking gift to order:', error);
      toast.error(t(attachGiftToOrderDrawerMessages.errorLinkingGift));
    } finally {
      setLinking(false);
    }
  };

  const handleClose = () => {
    setSelectedGift(null);
    onOpenChange(false);
  };

  return (
    <BaseDrawer open={open} onOpenChange={handleClose}>
      <DrawerHeader title={t(attachGiftToOrderDrawerMessages.title)} />

      <div className="p-4 space-y-4">
        <div className="bg-card rounded-lg p-3 border border-border">
          <h3 className="text-sm font-medium text-foreground mb-2">
            {t(attachGiftToOrderDrawerMessages.instructionsTitle)}
          </h3>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">
              {t(attachGiftToOrderDrawerMessages.instructionStep1)}
            </p>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
          </div>
        ) : gifts.length === 0 ? (
          <div className="space-y-4">
            <div className="text-center py-4">
              <p className="text-muted-foreground mb-4">
                {t(attachGiftToOrderDrawerMessages.noGiftsAvailable)}
              </p>
            </div>

            {/* Activate Order Instructions */}
            <div className="bg-card rounded-lg p-4 border border-border">
              <h3 className="text-sm font-medium text-foreground mb-3">
                {t(attachGiftToOrderDrawerMessages.activateOrderTitle)}
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-[var(--color-status-purple)] flex items-center justify-center text-white text-sm font-medium flex-shrink-0 mt-0.5">
                    1
                  </div>
                  <p className="text-foreground text-sm leading-relaxed">
                    {t(attachGiftToOrderDrawerMessages.activateStep1)}
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-[var(--color-status-purple)] flex items-center justify-center text-white text-sm font-medium flex-shrink-0 mt-0.5">
                    2
                  </div>
                  <p className="text-foreground text-sm leading-relaxed">
                    {t(attachGiftToOrderDrawerMessages.activateStep2)}
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-[var(--color-status-purple)] flex items-center justify-center text-white text-sm font-medium flex-shrink-0 mt-0.5">
                    3
                  </div>
                  <p className="text-foreground text-sm leading-relaxed">
                    {t(attachGiftToOrderDrawerMessages.activateStep3)}
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-[var(--color-status-purple)] flex items-center justify-center text-white text-sm font-medium flex-shrink-0 mt-0.5">
                    4
                  </div>
                  <p className="text-foreground text-sm leading-relaxed">
                    {t(attachGiftToOrderDrawerMessages.activateStep4)}
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-[var(--color-status-purple)] flex items-center justify-center text-white text-sm font-medium flex-shrink-0 mt-0.5">
                    5
                  </div>
                  <p className="text-foreground text-sm leading-relaxed">
                    {t(attachGiftToOrderDrawerMessages.activateStep5)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-white">
                {t(attachGiftToOrderDrawerMessages.selectGift)}
              </h3>
              <div className="grid grid-cols-2 gap-2">
                {gifts.map((gift) => (
                  <div
                    key={gift.id}
                    className={`cursor-pointer rounded-lg border-2 transition-colors p-2 ${
                      selectedGift?.id === gift.id
                        ? 'border-[var(--color-status-purple)] bg-[var(--color-status-purple-bg)]'
                        : 'border-border hover:border-border/80'
                    }`}
                    onClick={() => handleGiftSelect(gift)}
                  >
                    <div className="space-y-2">
                      <div className="aspect-square relative rounded-lg overflow-hidden bg-background">
                        <TgsOrImageGift
                          isImage={true}
                          gift={gift}
                          className="w-full h-full"
                          style={{ width: '100%', height: '100%' }}
                        />
                      </div>

                      <div className="text-center">
                        <p className="text-xs text-foreground font-medium truncate">
                          {collections.find((c) => c.id === gift.collectionId)
                            ?.name || 'Unknown Collection'}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          {gift.base_name}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {selectedGift && (
              <div className="pt-4 border-t border-border">
                <Button
                  onClick={handleLinkGift}
                  disabled={linking}
                  className="w-full bg-[var(--color-status-purple)] hover:bg-[var(--color-status-purple)]/90"
                >
                  {linking
                    ? t(attachGiftToOrderDrawerMessages.linking)
                    : t(attachGiftToOrderDrawerMessages.linkGiftToOrder)}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </BaseDrawer>
  );
}
