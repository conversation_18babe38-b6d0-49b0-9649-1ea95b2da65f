'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import type { LucideIcon } from 'lucide-react';
import type { ReactNode } from 'react';

interface DrawerHeaderProps {
  icon?: LucideIcon;
  iconClassName?: string;
  title: string;
  subtitle?: string;
  children?: ReactNode;
  className?: string;
}

export function DrawerHeader({
  icon: Icon,
  iconClassName = 'w-6 h-6 text-primary',
  title,
  subtitle,
  children,
  className = '',
}: DrawerHeaderProps) {
  return (
    <div className={`text-center mb-6 ${className}`}>
      <div className="flex items-center justify-center gap-2 mb-2">
        {Icon && <Icon className={iconClassName} />}
        <h2 className="text-xl font-bold text-foreground">{title}</h2>
      </div>
      {subtitle && (
        <Caption level="2" weight="3" className="text-muted-foreground">
          {subtitle}
        </Caption>
      )}
      {children}
    </div>
  );
}
