import { defineMessages } from 'react-intl';

export const errorMessages = defineMessages({
  'errors.auth.unauthenticated': {
    id: 'errors.auth.unauthenticated',
    defaultMessage: 'Authentication required.',
  },
  'errors.auth.permissionDenied': {
    id: 'errors.auth.permissionDenied',
    defaultMessage: 'Permission denied.',
  },
  'errors.auth.permissionDeniedWithOperation': {
    id: 'errors.auth.permissionDeniedWithOperation',
    defaultMessage: 'You can only perform {operation} for yourself.',
  },
  'errors.auth.adminOnly': {
    id: 'errors.auth.adminOnly',
    defaultMessage: 'Only admin users can perform this operation.',
  },
  'errors.auth.userNotFound': {
    id: 'errors.auth.userNotFound',
    defaultMessage: 'User not found.',
  },
  'errors.auth.tonWalletRequired': {
    id: 'errors.auth.tonWalletRequired',
    defaultMessage: 'User does not have a TON wallet address configured.',
  },

  // Validation errors
  'errors.validation.requiredField': {
    id: 'errors.validation.requiredField',
    defaultMessage: '{field} is required.',
  },
  'errors.validation.positiveAmountRequired': {
    id: 'errors.validation.positiveAmountRequired',
    defaultMessage: '{fieldName} must be greater than 0.',
  },
  'errors.validation.invalidOrderId': {
    id: 'errors.validation.invalidOrderId',
    defaultMessage: 'Valid order ID is required.',
  },
  'errors.validation.invalidCollectionId': {
    id: 'errors.validation.invalidCollectionId',
    defaultMessage: 'Valid collection ID is required.',
  },
  'errors.validation.invalidPrice': {
    id: 'errors.validation.invalidPrice',
    defaultMessage: 'Valid price is required.',
  },
  'errors.validation.invalidSecondaryMarketPrice': {
    id: 'errors.validation.invalidSecondaryMarketPrice',
    defaultMessage: 'Valid secondary market price is required.',
  },
  'errors.validation.invalidBotToken': {
    id: 'errors.validation.invalidBotToken',
    defaultMessage: 'Invalid bot token.',
  },
  'errors.validation.botTokenRequired': {
    id: 'errors.validation.botTokenRequired',
    defaultMessage: 'Bot token is required.',
  },
  'errors.validation.ownedGiftIdRequired': {
    id: 'errors.validation.ownedGiftIdRequired',
    defaultMessage: 'Owned gift ID is required.',
  },
  'errors.validation.userIdOrTgIdRequired': {
    id: 'errors.validation.userIdOrTgIdRequired',
    defaultMessage: 'Either userId or tgId is required.',
  },

  // Order errors
  'errors.order.orderNotFound': {
    id: 'errors.order.orderNotFound',
    defaultMessage: 'Order not found.',
  },
  'errors.order.insufficientBalance': {
    id: 'errors.order.insufficientBalance',
    defaultMessage: 'Insufficient balance.',
  },
  'errors.order.collectionNotFound': {
    id: 'errors.order.collectionNotFound',
    defaultMessage: 'Collection not found.',
  },
  'errors.order.collectionNotActive': {
    id: 'errors.order.collectionNotActive',
    defaultMessage: 'Collection is not active.',
  },
  'errors.order.onlyPaidOrdersSecondaryMarket': {
    id: 'errors.order.onlyPaidOrdersSecondaryMarket',
    defaultMessage:
      'Only orders with PAID status can be listed on secondary market.',
  },
  'errors.order.onlyBuyerCanSetSecondaryPrice': {
    id: 'errors.order.onlyBuyerCanSetSecondaryPrice',
    defaultMessage: 'Only the current buyer can set secondary market price.',
  },
  'errors.order.orderMustHaveBuyerAndSeller': {
    id: 'errors.order.orderMustHaveBuyerAndSeller',
    defaultMessage:
      'Order must have both buyer and seller to be listed on secondary market.',
  },
  'errors.order.secondaryPriceExceedsCollateral': {
    id: 'errors.order.secondaryPriceExceedsCollateral',
    defaultMessage:
      'Secondary market price cannot exceed total collateral of {totalCollateral} TON (buyer: {buyerAmount} TON + seller: {sellerAmount} TON).',
  },
  'errors.order.orderNotAvailableSecondaryMarket': {
    id: 'errors.order.orderNotAvailableSecondaryMarket',
    defaultMessage: 'Order is not available on secondary market.',
  },
  'errors.order.onlyPaidOrdersPurchasable': {
    id: 'errors.order.onlyPaidOrdersPurchasable',
    defaultMessage:
      'Only orders with PAID status can be purchased on secondary market.',
  },
  'errors.order.sellerCannotPurchaseOwnOrder': {
    id: 'errors.order.sellerCannotPurchaseOwnOrder',
    defaultMessage:
      'Seller cannot purchase their own order on secondary market.',
  },
  'errors.order.buyerCannotPurchaseSameOrder': {
    id: 'errors.order.buyerCannotPurchaseSameOrder',
    defaultMessage: 'You cannot purchase the same order again.',
  },
  'errors.order.secondaryPriceBelowMinimum': {
    id: 'errors.order.secondaryPriceBelowMinimum',
    defaultMessage: 'Secondary market price must be at least {minPrice} TON.',
  },
  'errors.order.orderMustBePaidStatus': {
    id: 'errors.order.orderMustBePaidStatus',
    defaultMessage: 'Order must be in paid status to send gift to relayer.',
  },
  'errors.order.orderMustBeGiftSentStatus': {
    id: 'errors.order.orderMustBeGiftSentStatus',
    defaultMessage:
      'Order must be in gift sent to relayer status to complete purchase.',
  },
  'errors.order.tooManyCreatedOrders': {
    id: 'errors.order.tooManyCreatedOrders',
    defaultMessage:
      'You already have 3 orders that need to be activated. Please activate existing orders before creating new ones.',
  },
  'errors.order.buyersCannotCreateMarketOrders': {
    id: 'errors.order.buyersCannotCreateMarketOrders',
    defaultMessage:
      'Buyers cannot create orders for market collections. Only sellers can create orders for market collections.',
  },

  // Withdrawal errors
  'errors.withdrawal.amountBelowMinimum': {
    id: 'errors.withdrawal.amountBelowMinimum',
    defaultMessage: 'Withdrawal amount must be at least {minAmount} TON.',
  },
  'errors.withdrawal.amountAboveMaximum': {
    id: 'errors.withdrawal.amountAboveMaximum',
    defaultMessage: 'Withdrawal amount cannot exceed {maxAmount} TON.',
  },
  'errors.withdrawal.amountExceeds24hLimit': {
    id: 'errors.withdrawal.amountExceeds24hLimit',
    defaultMessage:
      'Withdrawal amount exceeds 24-hour limit. You can withdraw up to {remainingLimit} TON. Limit resets at {resetAt}.',
  },
  'errors.withdrawal.insufficientAvailableBalance': {
    id: 'errors.withdrawal.insufficientAvailableBalance',
    defaultMessage: 'Insufficient available balance for withdrawal.',
  },
  'errors.withdrawal.amountTooSmallAfterFees': {
    id: 'errors.withdrawal.amountTooSmallAfterFees',
    defaultMessage: 'Amount too small after fees.',
  },

  // Telegram auth errors
  'errors.telegram.initDataRequired': {
    id: 'errors.telegram.initDataRequired',
    defaultMessage: 'initData is required.',
  },
  'errors.telegram.botTokenNotConfigured': {
    id: 'errors.telegram.botTokenNotConfigured',
    defaultMessage: 'Telegram bot token not configured.',
  },
  'errors.telegram.invalidTelegramData': {
    id: 'errors.telegram.invalidTelegramData',
    defaultMessage: 'Invalid Telegram data.',
  },
  'errors.telegram.firebaseAuthError': {
    id: 'errors.telegram.firebaseAuthError',
    defaultMessage: 'Firebase Auth error occurred.',
  },
  'errors.telegram.iamPermissionError': {
    id: 'errors.telegram.iamPermissionError',
    defaultMessage:
      'Firebase service account lacks required IAM permissions for custom token creation.',
  },

  // Generic errors
  'errors.generic.serverError': {
    id: 'errors.generic.serverError',
    defaultMessage: 'Server error occurred.',
  },
  'errors.generic.unknownError': {
    id: 'errors.generic.unknownError',
    defaultMessage: 'An unknown error occurred.',
  },
  'errors.generic.operationFailed': {
    id: 'errors.generic.operationFailed',
    defaultMessage: 'Operation failed. Please try again.',
  },
  'errors.generic.authenticationFailed': {
    id: 'errors.generic.authenticationFailed',
    defaultMessage: 'Authentication failed.',
  },

  // Gift errors
  'errors.gift.giftNotFound': {
    id: 'errors.gift.giftNotFound',
    defaultMessage: 'Gift not found.',
  },
  'errors.gift.giftInvalidStatus': {
    id: 'errors.gift.giftInvalidStatus',
    defaultMessage: 'Gift must have status "deposited" to be linked.',
  },
  'errors.gift.giftAlreadyLinked': {
    id: 'errors.gift.giftAlreadyLinked',
    defaultMessage: 'Gift is already linked to another order.',
  },
  'errors.gift.giftNotOwnedByUser': {
    id: 'errors.gift.giftNotOwnedByUser',
    defaultMessage: 'Gift does not belong to the current user.',
  },
  'errors.gift.giftCollectionMismatch': {
    id: 'errors.gift.giftCollectionMismatch',
    defaultMessage: 'Gift and order must be from the same collection.',
  },

  // Fulfill and resell errors
  'errors.fulfillAndResell.invalidParameters': {
    id: 'errors.fulfillAndResell.invalidParameters',
    defaultMessage: 'Order ID and resell price are required and must be valid.',
  },
  'errors.fulfillAndResell.orderNotFound': {
    id: 'errors.fulfillAndResell.orderNotFound',
    defaultMessage: 'Order not found.',
  },
  'errors.fulfillAndResell.notOrderBuyer': {
    id: 'errors.fulfillAndResell.notOrderBuyer',
    defaultMessage: 'You are not the buyer of this order.',
  },
  'errors.fulfillAndResell.invalidOrderStatus': {
    id: 'errors.fulfillAndResell.invalidOrderStatus',
    defaultMessage:
      'Order must have status "gift sent to relayer" to be resold.',
  },
  'errors.fulfillAndResell.insufficientBalance': {
    id: 'errors.fulfillAndResell.insufficientBalance',
    defaultMessage: 'Insufficient balance to create resell order.',
  },

  // Balance errors
  'errors.balance.insufficientLockedFunds': {
    id: 'errors.balance.insufficientLockedFunds',
    defaultMessage: 'Insufficient locked funds for this operation.',
  },
  'errors.balance.insufficientLockedFundsToSpend': {
    id: 'errors.balance.insufficientLockedFundsToSpend',
    defaultMessage: 'Insufficient locked funds to complete this transaction.',
  },
  'errors.balance.insufficientLockedFundsToUnlock': {
    id: 'errors.balance.insufficientLockedFundsToUnlock',
    defaultMessage: 'Insufficient locked funds to unlock the requested amount.',
  },

  // Proposal errors
  'errors.proposal.failedToAccept': {
    id: 'errors.proposal.failedToAccept',
    defaultMessage: 'Failed to accept proposal. Please try again.',
  },
  'errors.proposal.failedToCancel': {
    id: 'errors.proposal.failedToCancel',
    defaultMessage: 'Failed to cancel proposal. Please try again.',
  },
  'errors.proposal.failedToPropose': {
    id: 'errors.proposal.failedToPropose',
    defaultMessage: 'Failed to create proposal. Please try again.',
  },
  'errors.proposal.invalidArguments': {
    id: 'errors.proposal.invalidArguments',
    defaultMessage: 'Invalid proposal parameters provided.',
  },
});
