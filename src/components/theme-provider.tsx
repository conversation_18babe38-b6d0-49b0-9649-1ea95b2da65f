'use client';

import { useEffect } from 'react';

import { useThemeSwitcher } from '@/hooks/use-theme-switcher';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { theme } = useThemeSwitcher();

  // Apply theme to document on mount and theme changes
  useEffect(() => {
    const root = document.documentElement;

    // Remove all theme classes
    root.classList.remove('dark', 'black');

    // Add the current theme class (except for default)
    if (theme !== 'default') {
      root.classList.add(theme);
    }
  }, [theme]);

  return <>{children}</>;
}
