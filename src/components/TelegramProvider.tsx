'use client';

import { useLaunchParams } from '@telegram-apps/sdk-react';
import { AppRoot } from '@telegram-apps/telegram-ui';
import Image from 'next/image';
import { type PropsWithChildren, useEffect, useState } from 'react';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ErrorPage } from '@/components/ErrorPage';
import { useDidMount } from '@/hooks/use-did-mount';
import { useTelegramTheme } from '@/hooks/use-telegram-theme';
import { handleOrderDeepLink } from '@/utils/order-deep-link-utils';
import { handleReferralFromUrl } from '@/utils/referral-utils';

function TelegramRootInner({ children }: PropsWithChildren) {
  const lp = useLaunchParams();

  useTelegramTheme();

  return (
    <AppRoot
      appearance="dark"
      platform={['macos', 'ios'].includes(lp.tgWebAppPlatform) ? 'ios' : 'base'}
    >
      {children}
    </AppRoot>
  );
}

function FallbackRootInner({ children }: PropsWithChildren) {
  return (
    <AppRoot appearance="dark" platform="base">
      {children}
    </AppRoot>
  );
}

function RootInner({ children }: PropsWithChildren) {
  const [telegramAvailable, setTelegramAvailable] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const pathname = window.location.pathname;
    const isAdminOrAuth =
      pathname.startsWith('/admin') || pathname.startsWith('/auth');

    if (isAdminOrAuth && process.env.NODE_ENV === 'production') {
      setTelegramAvailable(false);
      setIsLoading(false);
      return;
    }

    try {
      if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
        setTelegramAvailable(true);
      } else {
        setTelegramAvailable(false);
      }
    } catch {
      setTelegramAvailable(false);
    }

    setIsLoading(false);
  }, []);

  if (isLoading) {
    return (
      <div
        className="bg-background text-foreground min-h-screen flex items-center justify-center"
        suppressHydrationWarning
      />
    );
  }

  if (telegramAvailable) {
    return <TelegramRootInner>{children}</TelegramRootInner>;
  } else {
    return <FallbackRootInner>{children}</FallbackRootInner>;
  }
}

export function TelegramProvider(props: PropsWithChildren) {
  const didMount = useDidMount();

  useEffect(() => {
    if (didMount) {
      handleReferralFromUrl();
      handleOrderDeepLink();
    }
  }, [didMount]);

  return (
    <ErrorBoundary fallback={ErrorPage}>
      {didMount ? (
        <RootInner {...props} />
      ) : (
        <div
          className="bg-background text-foreground min-h-screen flex items-center justify-center"
          suppressHydrationWarning
        >
          <Image
            priority
            width={130}
            height={130}
            src="/telegram-loader.gif"
            alt="telegram-loader"
          />
        </div>
      )}
    </ErrorBoundary>
  );
}
