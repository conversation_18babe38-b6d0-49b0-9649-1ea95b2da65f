import { defineMessages } from 'react-intl';

export const orderDetailsContentMessages = defineMessages({
  share: {
    id: 'orderDetails.content.share',
    defaultMessage: 'Share',
  },
  buy: {
    id: 'orderDetails.content.buy',
    defaultMessage: 'Buy',
  },
  action: {
    id: 'orderDetails.content.action',
    defaultMessage: 'Action',
  },
  fulfill: {
    id: 'orderDetails.content.fulfill',
    defaultMessage: 'Fulfill',
  },
  showResellHistory: {
    id: 'orderDetails.content.showResellHistory',
    defaultMessage: 'Show Resell History',
  },
  insufficientBalance: {
    id: 'orderDetails.content.insufficientBalance',
    defaultMessage: 'Insufficient balance to complete this action',
  },
  orderIdNotAvailable: {
    id: 'shareLink.orderIdNotAvailable',
    defaultMessage: 'Order ID not available',
  },
  checkOutOrder: {
    id: 'shareLink.checkOutOrder',
    defaultMessage: 'Check out this order!',
  },

  failedToShare: {
    id: 'shareLink.failedToShare',
    defaultMessage: 'Failed to share order',
  },
});
