import { useIntl } from 'react-intl';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';
import { getOrderDisplayNumber } from '@/services/order-service';

import { orderDetailsHeaderSectionMessages } from './intl/order-details-header-section.messages';

interface OrderDetailsHeaderSectionProps {
  collection: CollectionEntity | null;
  order: OrderEntity;
  className?: string;
}

export function OrderDetailsHeaderSection({
  collection,
  order,
  className,
}: OrderDetailsHeaderSectionProps) {
  const { formatMessage: t } = useIntl();
  return (
    <div className={cn('text-center space-y-2', className)}>
      <div className="flex items-center justify-center gap-3">
        <div className="text-xl font-bold text-foreground">
          {collection?.name ||
            t(orderDetailsHeaderSectionMessages.unknownCollection)}
        </div>
        <Badge
          variant="secondary"
          className="bg-[var(--color-status-blue-bg)] text-[var(--color-status-blue)] border-[var(--color-status-blue-border)] font-semibold text-sm px-2 py-1"
        >
          {getOrderDisplayNumber(order)}
        </Badge>
      </div>
    </div>
  );
}
