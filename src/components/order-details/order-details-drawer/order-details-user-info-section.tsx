import { Caption } from '@telegram-apps/telegram-ui';
import { Loader2, User } from 'lucide-react';
import { useIntl } from 'react-intl';

import { ADMIN_DEFAULT_NAME } from '@/core.constants';
import type { UserEntity } from '@/mikerudenko/marketplace-shared';

import { orderDetailsUserInfoSectionMessages } from './intl/order-details-user-info-section.messages';

interface OrderDetailsUserInfoSectionProps {
  userInfo: UserEntity | null;
  loading: boolean;
  userLabel: string;
}

export function OrderDetailsUserInfoSection({
  userInfo,
  loading,
  userLabel,
}: OrderDetailsUserInfoSectionProps) {
  const { formatMessage: t } = useIntl();
  return (
    <div className="bg-card/50 rounded-2xl p-4 border border-border/30 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-white" />
        </div>
        <span className="text-foreground font-medium">{userLabel}</span>
      </div>
      {loading ? (
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="w-4 h-4 animate-spin" />
          <Caption level="2" weight="3">
            {t(orderDetailsUserInfoSectionMessages.loading)}
          </Caption>
        </div>
      ) : userInfo ? (
        <div className="space-y-1">
          <p className="font-medium text-foreground">
            {userInfo.role === 'admin'
              ? ADMIN_DEFAULT_NAME
              : userInfo.displayName || userInfo.email || 'Anonymous User'}
          </p>
          {userInfo.email &&
            userInfo.displayName &&
            userInfo.role !== 'admin' && (
              <Caption level="2" weight="3" className="text-muted-foreground">
                {userInfo.email}
              </Caption>
            )}
        </div>
      ) : (
        <Caption level="2" weight="3" className="text-muted-foreground">
          {t(orderDetailsUserInfoSectionMessages.anonymousUser)}
        </Caption>
      )}
    </div>
  );
}
