import { Loader2, X } from 'lucide-react';
import type { ReactNode } from 'react';
import { useIntl } from 'react-intl';

import { AuthWrapper } from '@/components/auth/auth-wrapper';
import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { Button } from '@/components/ui/button';

import { orderDetailsActionButtonsMessages } from './intl/order-details-action-buttons.messages';

interface OrderDetailsActionButtonsProps {
  primaryAction: {
    label: ReactNode;
    onClick: () => void;
    loading: boolean;
    disabled?: boolean;
  };
  secondaryAction?: {
    label: ReactNode;
    onClick: () => void;
    disabled?: boolean;
  };
  onClose?: () => void;
  actionLoading: boolean;
  shouldShowCloseButton?: boolean;
}

export function OrderDetailsActionButtons({
  primaryAction,
  secondaryAction,
  onClose,
  actionLoading,
  shouldShowCloseButton = false,
}: OrderDetailsActionButtonsProps) {
  const { formatMessage: t } = useIntl();
  return (
    <div className="space-y-3">
      <AuthWrapper>
        <ConfirmWrapper>
          <Button
            variant="default"
            onClick={primaryAction.onClick}
            disabled={primaryAction.loading || primaryAction.disabled}
            className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground border-0 rounded-2xl"
          >
            {primaryAction.loading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
                {t(orderDetailsActionButtonsMessages.processing)}
              </>
            ) : (
              primaryAction.label
            )}
          </Button>
        </ConfirmWrapper>
      </AuthWrapper>

      {secondaryAction && (
        <Button
          onClick={secondaryAction.onClick}
          disabled={secondaryAction.disabled || actionLoading}
          variant="outline"
          className="w-full h-12 border-primary text-primary hover:bg-primary/10 bg-transparent rounded-2xl"
        >
          {secondaryAction.label}
        </Button>
      )}

      {shouldShowCloseButton && onClose && (
        <Button
          variant="destructive"
          onClick={onClose}
          className="w-full h-12 bg-[var(--color-status-red-bg)] border-[var(--color-status-red-border)] text-white hover:bg-[var(--color-status-red)]/30 hover:text-[var(--color-status-red)] rounded-2xl text-base font-medium flex items-center"
          disabled={actionLoading}
        >
          <span>{t(orderDetailsActionButtonsMessages.close)}</span>
          <X className="w-5 h-5 -ml-1.25 translate-y-[1px]" />
        </Button>
      )}
    </div>
  );
}
