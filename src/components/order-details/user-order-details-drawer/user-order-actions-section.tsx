import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import {
  canCancelOrder,
  canCreateSecondaryMarketOrder,
  hasSecondaryMarketPrice,
} from '@/services/order-service';

import { userOrderActionsSectionMessages } from './intl/user-order-actions-section.messages';

interface UserOrderActionsSectionProps {
  order: OrderEntity;
  currentUserId?: string;
  onCancelOrder: () => void;
  onCreateSecondaryMarketOrder: () => void;
  onShowResellHistory?: () => void;
}

export function UserOrderActionsSection({
  order,
  currentUserId,
  onCancelOrder,
  onCreateSecondaryMarketOrder,
  onShowResellHistory,
}: UserOrderActionsSectionProps) {
  const { formatMessage: t } = useIntl();
  const canCancel = canCancelOrder(order, currentUserId);
  const canCreateSecondary = canCreateSecondaryMarketOrder(
    order,
    currentUserId,
  );
  const hasSecondaryPrice = hasSecondaryMarketPrice(order);
  // TODO: Temporarily hidden - resell tx history feature
  const shouldShowResellHistory = false;
  // order.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  if (!canCancel && !canCreateSecondary && !shouldShowResellHistory) {
    return null;
  }

  const buttons = [];

  if (canCreateSecondary) {
    buttons.push(
      <Button
        key="resale"
        onClick={onCreateSecondaryMarketOrder}
        className="flex-1 h-12 bg-primary hover:bg-primary/90 text-primary-foreground rounded-2xl text-base font-medium flex items-center justify-center"
      >
        <span>
          {hasSecondaryPrice
            ? t(userOrderActionsSectionMessages.updateResaleOrder)
            : t(userOrderActionsSectionMessages.createResaleOrder)}
        </span>
      </Button>,
    );
  }

  if (shouldShowResellHistory && onShowResellHistory) {
    buttons.push(
      <Button
        key="history"
        onClick={onShowResellHistory}
        variant="outline"
        className="flex-1 h-12 bg-primary/20 border-primary/30 text-foreground hover:bg-primary/30 hover:text-primary rounded-2xl text-base font-medium flex items-center justify-center"
      >
        <span>{t(userOrderActionsSectionMessages.showResellHistory)}</span>
      </Button>,
    );
  }

  if (canCancel) {
    buttons.push(
      <Button
        key="cancel"
        variant="outline"
        onClick={onCancelOrder}
        className="flex-1 h-12 bg-muted/50 border-muted-foreground/50 text-foreground hover:bg-muted/60 hover:text-muted-foreground rounded-2xl text-base font-medium flex items-center justify-center"
      >
        <span>{t(userOrderActionsSectionMessages.cancelOrder)}</span>
      </Button>,
    );
  }

  if (buttons.length === 0) {
    return null;
  }

  return (
    <div className="border-border/30">
      <div className="flex gap-3">{buttons}</div>
    </div>
  );
}
