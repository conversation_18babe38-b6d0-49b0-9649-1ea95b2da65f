import { Alert<PERSON>riangle, Clock, ExternalLink, Gift } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';
import { TELEGRAM_BOT_URL } from '@/core.constants';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import { UserType } from '@/mikerudenko/marketplace-shared';
import {
  shouldShowFreezeWarning,
  shouldShowGiftReadySection,
  shouldShowGiftRefundSection,
} from '@/services/order-service';

import { userOrderStatusAlertsMessages } from './intl/user-order-status-alerts.messages';

interface UserOrderStatusAlertsProps {
  order: OrderEntity;
  userType: UserType;
  isFreezed: boolean;
}

interface StatusAlertProps {
  variant: 'warning' | 'info' | 'success' | 'gradient' | 'refund';
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  buttonText?: string;
  onButtonClick?: () => void;
}

const variantStyles = {
  warning: {
    container:
      'bg-[var(--color-status-yellow-bg)] border border-[var(--color-status-yellow-border)]',
    icon: 'text-[var(--color-status-yellow)]',
    title: 'text-[var(--color-status-yellow)]',
  },
  info: {
    container:
      'bg-[var(--color-status-blue-bg)] border border-[var(--color-status-blue-border)]',
    icon: 'text-[var(--color-status-blue)]',
    title: 'text-[var(--color-status-blue)]',
  },
  success: {
    container:
      'bg-[var(--color-status-green-bg)] border border-[var(--color-status-green-border)]',
    icon: 'text-[var(--color-status-green)]',
    title: 'text-[var(--color-status-green)]',
  },
  gradient: {
    container:
      'bg-gradient-to-r from-[var(--color-status-purple-bg)] to-[var(--color-status-pink-bg)] border border-[var(--color-status-purple-border)]',
    icon: 'text-[var(--color-status-purple)]',
    title: 'text-[var(--color-status-purple)]',
  },
  refund: {
    container:
      'bg-[var(--color-status-orange-bg)] border border-[var(--color-status-orange-border)]',
    icon: 'text-[var(--color-status-orange)]',
    title: 'text-[var(--color-status-orange)]',
  },
};

const buttonStyles = {
  gradient:
    'bg-[var(--color-status-purple)] hover:bg-[var(--color-status-purple)]/90',
  refund:
    'bg-[var(--color-status-orange)] hover:bg-[var(--color-status-orange)]/90',
};

function StatusAlert({
  variant,
  icon: Icon,
  title,
  description,
  buttonText,
  onButtonClick,
}: StatusAlertProps) {
  const styles = variantStyles[variant];

  return (
    <div className={`${styles.container} rounded-2xl p-4`}>
      <div className="flex items-center gap-2 mb-2">
        <Icon className={`w-5 h-5 ${styles.icon}`} />
        <span className={`${styles.title} font-semibold`}>{title}</span>
      </div>
      <p className="text-muted-foreground text-sm">{description}</p>
      {buttonText && onButtonClick && (
        <Button
          size="sm"
          className={`w-full ${buttonStyles[variant as keyof typeof buttonStyles]} text-white rounded-xl`}
          onClick={onButtonClick}
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          {buttonText}
        </Button>
      )}
    </div>
  );
}

export function UserOrderStatusAlerts({
  order,
  userType,
  isFreezed,
}: UserOrderStatusAlertsProps) {
  const { formatMessage: t } = useIntl();
  const showFreezeWarning = shouldShowFreezeWarning(order, userType, isFreezed);
  const showGiftReady = shouldShowGiftReadySection(order, userType);
  const showGiftRefund = shouldShowGiftRefundSection(order, userType);

  const alerts = [
    {
      show: showFreezeWarning,
      props: {
        variant: 'warning' as const,
        icon: AlertTriangle,
        title: t(userOrderStatusAlertsMessages.freezePeriodActive),
        description: t(userOrderStatusAlertsMessages.freezePeriodDescription),
      },
    },
    {
      show: !isFreezed && userType === UserType.SELLER && !order.deadline,
      props: {
        variant: 'info' as const,
        icon: Clock,
        title: t(userOrderStatusAlertsMessages.waitingForTransfer),
        description: t(
          userOrderStatusAlertsMessages.waitingForTransferDescription,
        ),
      },
    },
    {
      show: !isFreezed && userType === UserType.SELLER && order.deadline,
      props: {
        variant: 'success' as const,
        icon: Gift,
        title: t(userOrderStatusAlertsMessages.readyToSend),
        description: t(userOrderStatusAlertsMessages.readyToSendDescription),
      },
    },
    {
      show: showGiftReady,
      props: {
        variant: 'gradient' as const,
        icon: Gift,
        title: t(userOrderStatusAlertsMessages.giftReady),
        description: t(userOrderStatusAlertsMessages.giftReadyDescription),
        buttonText: t(userOrderStatusAlertsMessages.openBotToClaim),
        onButtonClick: () => window.open(TELEGRAM_BOT_URL, '_blank'),
      },
    },
    {
      show: showGiftRefund,
      props: {
        variant: 'refund' as const,
        icon: AlertTriangle,
        title: t(userOrderStatusAlertsMessages.giftRefundAvailable),
        description: t(userOrderStatusAlertsMessages.giftRefundDescription),
        buttonText: t(userOrderStatusAlertsMessages.openBotForRefund),
        onButtonClick: () => window.open(TELEGRAM_BOT_URL, '_blank'),
      },
    },
  ];

  const visibleAlerts = alerts.filter((alert) => alert.show);

  if (visibleAlerts.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {visibleAlerts.map((alert, index) => (
        <StatusAlert key={index} {...alert.props} />
      ))}
    </div>
  );
}
