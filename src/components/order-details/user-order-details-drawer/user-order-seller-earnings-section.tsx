'use client';

import { ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import { PriceLabel } from '@/components/shared/price-label';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';

import { userOrderSellerEarningsSectionMessages } from './intl/user-order-seller-earnings-section.messages';

interface UserOrderSellerEarningsSectionProps {
  order: OrderEntity;
}

export function UserOrderSellerEarningsSection({
  order,
}: UserOrderSellerEarningsSectionProps) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  if (
    !order.reseller_earnings_for_seller ||
    order.reseller_earnings_for_seller <= 0
  ) {
    return null;
  }

  return (
    <div className="space-y-4">
      <Collapsible defaultOpen={false} open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-[#1e2337]/50 rounded-lg hover:bg-[#1e2337]/70 transition-colors duration-200">
          <span className="text-[#f5f5f5] font-medium">
            {t(userOrderSellerEarningsSectionMessages.resaleEarnings)}
          </span>
          <ChevronDown
            className={cn(
              'h-4 w-4 text-[#708499] transition-transform duration-300 ease-in-out',
              isOpen && 'rotate-180',
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
          <div className="px-4 pb-4 space-y-4 bg-background/50 rounded-lg">
            <div className="bg-[var(--color-status-green-bg)] border border-[var(--color-status-green-border)] rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-[var(--color-status-green)] text-sm font-medium">
                  {t(
                    userOrderSellerEarningsSectionMessages.totalEarningsFromResales,
                  )}
                </span>
                <PriceLabel
                  amount={order.reseller_earnings_for_seller || 0}
                  size={20}
                  className="text-xl font-bold text-[var(--color-status-green)]"
                  showUnit
                />
              </div>
              <p className="text-xs text-[var(--color-status-green)]/80 mt-2">
                {t(userOrderSellerEarningsSectionMessages.earningsDescription)}
              </p>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
