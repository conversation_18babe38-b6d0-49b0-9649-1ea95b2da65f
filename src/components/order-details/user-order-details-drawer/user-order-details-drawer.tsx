'use client';

import { Share, X } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import { OrderDetailsFeesSection } from '@/components/order-details/order-details-fees-section';
import { OrderStatusBadge } from '@/components/shared/order-status-badge';
import { OrderTraitsSection } from '@/components/shared/order-traits-section';
import { Button } from '@/components/ui/button';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { OrderActors } from '@/components/ui/order-actors';
import { useOrderGift } from '@/hooks/use-order-gift';
import { useOrderTimers } from '@/hooks/use-order-timers';
import { useShareLink } from '@/hooks/use-share-link';
import { cn } from '@/lib/utils';
import type { OrderEntity, UserType } from '@/mikerudenko/marketplace-shared';
import { OrderStatus, Role } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';
import { shouldShowSellerEarnings } from '@/services/order-service';

import { ResellOrderPriceDrawer } from '../../../app/(app)/marketplace/resell/resell-order-price-drawer';
import { CancelOrderDrawer } from '../../../app/(app)/orders/cancel-order-drawer';
import { orderDetailsActionButtonsMessages } from '../order-details-drawer/intl/order-details-action-buttons.messages';
import { OrderDetailsBaseDrawer } from '../order-details-drawer/order-details-base-drawer';
import {
  UserOrderActionsSection,
  UserOrderDeadlineSection,
  UserOrderDetailsSection,
  UserOrderImageSection,
  UserOrderSellerEarningsSection,
  UserOrderStatusAlerts,
} from '.';

interface UserOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType: UserType;
  onOrderUpdate: () => void;
}

export function UserOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderUpdate,
}: UserOrderDetailsDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { collections, currentUser } = useRootContext();
  const { gift } = useOrderGift(order);
  const [showCancelDrawer, setShowCancelDrawer] = useState(false);
  const [showResellPriceDrawer, setShowResellPriceDrawer] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);
  const { handleShare, isSharing } = useShareLink({ orderId: order?.id });

  const collection =
    collections.find((c) => c.id === order?.collectionId) || null;
  const { timeLeft, isFreezed } = useOrderTimers({ order, collection });

  const handleCancelOrder = () => setShowCancelDrawer(true);
  const handleCreateSecondaryMarketOrder = () => setShowResellPriceDrawer(true);
  // TODO: Temporarily hidden - resell tx history feature
  const handleShowResellHistory = () => {
    // setShowResellHistory(true);
  };

  const handleOrderCancelled = () => {
    onOrderUpdate();
    onOpenChange(false);
  };

  const handleOrderResold = () => {
    onOrderUpdate();
    setShowResellPriceDrawer(false);
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <>
      <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
        <div className="w-full mx-auto">
          <UserOrderImageSection
            collection={collection}
            order={order}
            top={<OrderStatusBadge order={order} />}
            bottom={
              <Button
                onClick={handleShare}
                disabled={isSharing}
                variant="outline"
                className="ml-auto mb-2 mr-2 border-border text-foreground hover:bg-card/50 bg-transparent rounded-2xl"
              >
                <Share
                  className={cn('w-4 h-4', isSharing && 'animate-pulse')}
                />
              </Button>
            }
          />
        </div>

        <UserOrderDetailsSection collection={collection} order={order} />

        {order.status === OrderStatus.PAID && (
          <div className="space-y-3">
            <UserOrderDeadlineSection
              {...{
                order,
                userType,
                timeLeft,
              }}
            />
            <UserOrderStatusAlerts
              {...{
                order,
                userType,
                isFreezed,
              }}
            />
          </div>
        )}

        {gift && <OrderTraitsSection gift={gift} />}

        <OrderDetailsFeesSection order={order} />

        {/* <OrderDetailsDate
          updatedAt={firebaseTimestampToDate(order.updatedAt)}
        /> */}

        {shouldShowSellerEarnings(currentUser, order) && (
          <UserOrderSellerEarningsSection order={order} />
        )}

        {currentUser?.role === Role.ADMIN && (
          <OrderActors buyerId={order.buyerId} sellerId={order.sellerId} />
        )}

        <div>
          <UserOrderActionsSection
            order={order}
            currentUserId={currentUser?.id}
            onCancelOrder={handleCancelOrder}
            onCreateSecondaryMarketOrder={handleCreateSecondaryMarketOrder}
            onShowResellHistory={handleShowResellHistory}
          />
          <div className="space-y-3 pt-3">
            <Button
              variant="destructive"
              onClick={() => onOpenChange(false)}
              className="w-full h-12 bg-[var(--color-status-red-bg)] border-[var(--color-status-red-border)] text-white hover:bg-[var(--color-status-red)]/30 hover:text-[var(--color-status-red)] rounded-2xl text-base font-medium flex items-center"
            >
              <span>{t(orderDetailsActionButtonsMessages.close)}</span>
              <X className="w-5 h-5 -ml-1.25 translate-y-[1px]" />
            </Button>
          </div>
        </div>
      </OrderDetailsBaseDrawer>

      <CancelOrderDrawer
        open={showCancelDrawer}
        onOpenChange={setShowCancelDrawer}
        order={order}
        onOrderCancelled={handleOrderCancelled}
      />

      <ResellOrderPriceDrawer
        open={showResellPriceDrawer}
        onOpenChange={setShowResellPriceDrawer}
        order={order}
        onOrderResold={handleOrderResold}
      />

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}
    </>
  );
}
