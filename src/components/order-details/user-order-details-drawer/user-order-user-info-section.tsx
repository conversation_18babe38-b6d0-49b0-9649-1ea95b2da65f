import { Avatar } from '@telegram-apps/telegram-ui';
import { Loader2, User } from 'lucide-react';
import { useIntl } from 'react-intl';

import { ADMIN_DEFAULT_NAME } from '@/core.constants';
import type { UserEntity } from '@/mikerudenko/marketplace-shared';
import { UserType } from '@/mikerudenko/marketplace-shared';

import { userOrderUserInfoSectionMessages } from './intl/user-order-user-info-section.messages';

interface UserOrderUserInfoSectionProps {
  otherUser: UserEntity | null;
  userType: UserType;
  loadingUser: boolean;
}

export function UserOrderUserInfoSection({
  otherUser,
  userType,
  loadingUser,
}: UserOrderUserInfoSectionProps) {
  const { formatMessage: t } = useIntl();
  const otherUserRole = userType === UserType.SELLER ? 'Buyer' : 'Seller';

  return (
    <div className="space-y-3">
      <h3 className="font-semibold text-foreground text-center">
        {otherUserRole} Information
      </h3>

      {loadingUser ? (
        <div className="flex items-center justify-center gap-3 p-4 bg-card rounded-2xl">
          <Loader2 className="w-5 h-5 animate-spin text-primary" />
          <span className="text-muted-foreground">Loading user info...</span>
        </div>
      ) : otherUser ? (
        <div className="flex items-center gap-4 p-4 bg-card rounded-2xl">
          {otherUser.photoURL ? (
            <Avatar
              size={48}
              src={otherUser.photoURL}
              className="ring-2 ring-primary/20"
            />
          ) : (
            <div className="w-12 h-12 bg-border rounded-full flex items-center justify-center ring-2 ring-primary/20">
              <User className="w-6 h-6 text-muted-foreground" />
            </div>
          )}
          <div className="flex-1">
            <p className="text-foreground font-semibold text-lg">
              {otherUser.role === 'admin'
                ? ADMIN_DEFAULT_NAME
                : otherUser.displayName || otherUser.name || 'Anonymous User'}
            </p>
            <p className="text-primary text-sm font-medium">{otherUserRole}</p>
          </div>
        </div>
      ) : (
        <div className="p-4 bg-card rounded-2xl text-center">
          <p className="text-muted-foreground text-sm">
            {t(userOrderUserInfoSectionMessages.noUserAssigned, {
              role: otherUserRole.toLowerCase(),
            })}
          </p>
        </div>
      )}
    </div>
  );
}
