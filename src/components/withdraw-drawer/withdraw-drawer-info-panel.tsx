import { AlertTriangle } from 'lucide-react';
import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

interface WithdrawalStatus {
  maxLimit: number;
  remainingLimit: number;
  resetAt: string;
}

interface AppConfig {
  withdrawal_fee: number;
}

interface WithdrawDrawerInfoPanelProps {
  appConfig: AppConfig;
  availableBalance: number;
  withdrawalStatus?: WithdrawalStatus | null;
}

interface InfoRowProps {
  label: string;
  value: string | number;
  showTonLogo?: boolean;
  isDate?: boolean;
}

function InfoRow({
  label,
  value,
  showTonLogo = true,
  isDate = false,
}: InfoRowProps) {
  return (
    <div className="flex justify-between items-center py-1">
      <span className="text-[#708499]">{label}</span>
      {isDate ? (
        <span className="text-[#6ab2f2] font-semibold text-xs">
          {new Date(value).toLocaleString()}
        </span>
      ) : (
        <div className="flex items-center gap-1">
          <span className="text-[#6ab2f2] font-semibold">{value}</span>
          {showTonLogo && <TonLogo size={24} />}
        </div>
      )}
    </div>
  );
}

export function WithdrawDrawerInfoPanel({
  appConfig,
  availableBalance,
  withdrawalStatus,
}: WithdrawDrawerInfoPanelProps) {
  const { formatMessage: t } = useIntl();

  return (
    <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
      <div className="w-full flex items-start gap-3">
        <AlertTriangle className="w-5 h-5 text-[#ec3942] flex-shrink-0 mt-0.5" />
        <div className="w-full text-sm">
          <p className="w-full font-medium text-[#f5f5f5] mb-2">
            {t(withdrawDrawerMessages.withdrawalInformation)}
          </p>
          <div className="space-y-2">
            <InfoRow
              label={t(withdrawDrawerMessages.minimumWithdrawal)}
              value="1"
            />
            <InfoRow
              label={t(withdrawDrawerMessages.withdrawalFee)}
              value={appConfig.withdrawal_fee || 0.1}
            />
            <InfoRow
              label={t(withdrawDrawerMessages.availableBalance)}
              value={availableBalance.toFixed(2)}
            />
            {withdrawalStatus && (
              <>
                <InfoRow
                  label={t(withdrawDrawerMessages.withdrawalLimit24h)}
                  value={withdrawalStatus.maxLimit}
                />
                <InfoRow
                  label={t(withdrawDrawerMessages.remainingLimit)}
                  value={withdrawalStatus.remainingLimit.toFixed(2)}
                />
                <InfoRow
                  label={t(withdrawDrawerMessages.limitResetsAt)}
                  value={withdrawalStatus.resetAt}
                  showTonLogo={false}
                  isDate={true}
                />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
