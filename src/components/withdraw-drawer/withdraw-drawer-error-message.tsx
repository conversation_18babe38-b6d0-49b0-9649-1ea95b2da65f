import { Caption } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

interface WithdrawalStatus {
  remainingLimit: number;
}

interface AppConfig {
  min_withdrawal_amount: number;
}

interface WithdrawDrawerErrorMessageProps {
  withdrawAmount: string;
  isValidAmount: boolean;
  appConfig?: AppConfig | null;
  availableBalance: number;
  withdrawalStatus?: WithdrawalStatus | null;
  isWalletConnected: boolean;
}

export function WithdrawDrawerErrorMessage({
  withdrawAmount,
  isValidAmount,
  appConfig,
  availableBalance,
  withdrawalStatus,
  isWalletConnected,
}: WithdrawDrawerErrorMessageProps) {
  const { formatMessage: t } = useIntl();

  if (!withdrawAmount && isWalletConnected) return null;

  const getErrorMessage = () => {
    if (!isWalletConnected) {
      return t(withdrawDrawerMessages.pleaseConnectWallet);
    }

    if (!withdrawAmount || isValidAmount) return null;

    const amount = parseFloat(withdrawAmount);

    if (amount < (appConfig?.min_withdrawal_amount ?? 1)) {
      return t(withdrawDrawerMessages.minimumWithdrawalAmount, {
        minAmount: appConfig?.min_withdrawal_amount ?? 1,
      });
    }

    if (amount > availableBalance) {
      return t(withdrawDrawerMessages.insufficientAvailableBalance);
    }

    if (withdrawalStatus && amount > withdrawalStatus.remainingLimit) {
      return t(withdrawDrawerMessages.exceeds24HourLimit, {
        remainingAmount: withdrawalStatus.remainingLimit.toFixed(2),
      });
    }

    return t(withdrawDrawerMessages.invalidAmount);
  };

  const errorMessage = getErrorMessage();

  if (!errorMessage) return null;

  return (
    <div className="text-center mt-4">
      <Caption level="2" weight="3" className="text-[#ec3942]">
        {errorMessage}
      </Caption>
    </div>
  );
}
