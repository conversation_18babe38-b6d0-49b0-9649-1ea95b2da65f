import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

interface WithdrawalStatus {
  remainingLimit: number;
}

interface WithdrawDrawerAmountInputProps {
  withdrawAmount: string;
  onWithdrawAmountChange: (amount: string) => void;
  availableBalance: number;
  withdrawalStatus?: WithdrawalStatus | null;
  loading: boolean;
  statusLoading: boolean;
}

export function WithdrawDrawerAmountInput({
  withdrawAmount,
  onWithdrawAmountChange,
  availableBalance,
  withdrawalStatus,
  loading,
  statusLoading,
}: WithdrawDrawerAmountInputProps) {
  const { formatMessage: t } = useIntl();

  const handleMaxClick = () => {
    const maxAmount = Math.min(
      availableBalance,
      withdrawalStatus?.remainingLimit ?? availableBalance,
    );
    onWithdrawAmountChange(maxAmount.toString());
  };

  const maxInputValue = withdrawalStatus
    ? Math.min(availableBalance, withdrawalStatus.remainingLimit)
    : availableBalance;

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <Label
          htmlFor="withdraw-amount"
          className="text-sm font-medium text-[#f5f5f5]"
        >
          {t(withdrawDrawerMessages.withdrawAmountTon)}
        </Label>
        <Button onClick={handleMaxClick}>
          {t(withdrawDrawerMessages.max)}
        </Button>
      </div>
      <Input
        id="withdraw-amount"
        type="number"
        step="0.01"
        min="1"
        max={maxInputValue}
        placeholder={t(withdrawDrawerMessages.enterAmountToWithdraw)}
        value={withdrawAmount}
        onChange={(e) => onWithdrawAmountChange(e.target.value)}
        className="mt-2 bg-[#232e3c]/50 border-[#3a4a5c]/50 text-[#f5f5f5] placeholder:text-[#708499] focus:border-[#6ab2f2] focus:ring-[#6ab2f2]/20"
        disabled={loading || statusLoading}
      />
    </div>
  );
}
