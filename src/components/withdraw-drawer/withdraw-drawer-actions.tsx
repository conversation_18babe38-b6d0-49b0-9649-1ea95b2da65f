import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

interface WithdrawDrawerActionsProps {
  onWithdraw: () => void;
  onCancel: () => void;
  isValidAmount: boolean;
  loading: boolean;
  statusLoading: boolean;
  isWalletConnected: boolean;
  withdrawalStatus?: { remainingLimit: number } | null;
  withdrawAmount: string;
  netAmount: number;
}

export function WithdrawDrawerActions({
  onWithdraw,
  onCancel,
  isValidAmount,
  loading,
  statusLoading,
  isWalletConnected,
  withdrawalStatus,
  withdrawAmount,
  netAmount,
}: WithdrawDrawerActionsProps) {
  const { formatMessage: t } = useIntl();

  const isWithdrawDisabled =
    !isValidAmount ||
    loading ||
    statusLoading ||
    !isWalletConnected ||
    !withdrawalStatus;

  const renderWithdrawButtonContent = () => {
    if (loading) {
      return t(withdrawDrawerMessages.processing);
    }

    const baseText = t(withdrawDrawerMessages.withdraw);

    if (withdrawAmount && isValidAmount) {
      return (
        <>
          {baseText} &#40;{netAmount.toFixed(2)}{' '}
          <TonLogo className="-m-2" size={24} />
          <span className="-ml-1 translate-x-[1px]">&#41;</span>
        </>
      );
    }

    return baseText;
  };

  return (
    <div className="space-y-3 pt-4">
      <Button
        onClick={onWithdraw}
        disabled={isWithdrawDisabled}
        className="w-full h-12 bg-[#ec3942] hover:bg-[#ec3942]/90 text-white border-0 rounded-2xl"
      >
        {renderWithdrawButtonContent()}
      </Button>

      <Button
        variant="outline"
        onClick={onCancel}
        className="w-full h-12 bg-transparent border-[#3a4a5c]/50 text-[#708499] hover:bg-[#232e3c]/50 hover:text-[#f5f5f5] rounded-2xl"
        disabled={loading}
      >
        {t(withdrawDrawerMessages.cancel)}
      </Button>
    </div>
  );
}
