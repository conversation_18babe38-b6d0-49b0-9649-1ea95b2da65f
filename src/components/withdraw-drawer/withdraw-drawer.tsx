'use client';

import { useTonConnectUI } from '@tonconnect/ui-react';
import { httpsCallable } from 'firebase/functions';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

import { formatServerError } from '@/api/server-error-handler';
import { useWithdrawalStatus } from '@/components/withdraw-drawer/use-withdrawal-status';
import { useVisualViewport } from '@/hooks/use-visual-viewport';
import { firebaseFunctions, useRootContext } from '@/root-context';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';
import { WithdrawDrawerActions } from './withdraw-drawer-actions';
import { WithdrawDrawerAmountInput } from './withdraw-drawer-amount-input';
import { WithdrawDrawerCalculation } from './withdraw-drawer-calculation';
import { WithdrawDrawerErrorMessage } from './withdraw-drawer-error-message';
import { WithdrawDrawerHeader } from './withdraw-drawer-header';
import { WithdrawDrawerInfoPanel } from './withdraw-drawer-info-panel';
import { WithdrawDrawerLoading } from './withdraw-drawer-loading';
import {
  calculateAvailableBalance,
  calculateNetAmount,
  calculateWithdrawFee,
  validateWithdrawAmount,
} from './withdraw-drawer-utils';

interface WithdrawDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface WithdrawResponse {
  success: boolean;
  message: string;
  netAmount: number;
  feeAmount: number;
  transactionHash: string;
}

export function WithdrawDrawer({ open, onOpenChange }: WithdrawDrawerProps) {
  const { formatMessage: t } = useIntl();
  const [tonConnectUI] = useTonConnectUI();
  const { currentUser, appConfig } = useRootContext();
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const {
    withdrawalStatus,
    loading: statusLoading,
    refetch: refetchStatus,
  } = useWithdrawalStatus(
    currentUser,
    appConfig?.max_withdrawal_amount ?? Number.MAX_SAFE_INTEGER,
  );
  const drawerContentRef = useVisualViewport({ enabled: open, offset: 64 });

  const isValidAmount = validateWithdrawAmount(
    withdrawAmount,
    appConfig,
    currentUser?.balance,
    withdrawalStatus,
  );

  const availableBalance = calculateAvailableBalance(currentUser?.balance);
  const withdrawFee = calculateWithdrawFee(withdrawAmount, appConfig);
  const netAmount = calculateNetAmount(withdrawAmount, withdrawFee);

  const handleWithdraw = async () => {
    if (!isValidAmount || !appConfig) {
      toast.error(t(withdrawDrawerMessages.invalidWithdrawalAmount));
      return;
    }

    if (!tonConnectUI.account?.address) {
      toast.error(t(withdrawDrawerMessages.pleaseConnectWalletFirst));
      return;
    }

    if (!currentUser?.ton_wallet_address) {
      toast.error(t(withdrawDrawerMessages.noWalletAddressFound));
      return;
    }

    try {
      setLoading(true);

      const amount = parseFloat(withdrawAmount);

      const withdrawFundsFunction = httpsCallable<
        { amount: number },
        WithdrawResponse
      >(firebaseFunctions, 'withdrawFunds');

      const result = await withdrawFundsFunction({ amount });

      console.log('Withdrawal result:', result.data);
      toast.success(
        t(withdrawDrawerMessages.withdrawalSuccessful, {
          hash: result.data.transactionHash,
        }),
      );

      setWithdrawAmount('');
      onOpenChange(false);

      await Promise.all([refetchStatus()]);
    } catch (error) {
      console.error('Withdrawal failed:', error);
      const errorMessage = formatServerError(error, t);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setWithdrawAmount('');
    onOpenChange(false);
  };

  return (
    <Drawer.Root
      open={open}
      onOpenChange={onOpenChange}
      shouldScaleBackground
      modal={true}
      dismissible={true}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content
          ref={drawerContentRef}
          className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none max-h-[90vh]"
        >
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              <WithdrawDrawerHeader />

              {!appConfig ? (
                <WithdrawDrawerLoading />
              ) : (
                <>
                  <WithdrawDrawerInfoPanel
                    appConfig={appConfig}
                    availableBalance={availableBalance}
                    withdrawalStatus={withdrawalStatus}
                  />

                  <div className="space-y-4">
                    <WithdrawDrawerAmountInput
                      withdrawAmount={withdrawAmount}
                      onWithdrawAmountChange={setWithdrawAmount}
                      availableBalance={availableBalance}
                      withdrawalStatus={withdrawalStatus}
                      loading={loading}
                      statusLoading={statusLoading}
                    />

                    <WithdrawDrawerCalculation
                      withdrawAmount={withdrawAmount}
                      withdrawFee={withdrawFee}
                      netAmount={netAmount}
                    />

                    <WithdrawDrawerErrorMessage
                      withdrawAmount={withdrawAmount}
                      isValidAmount={isValidAmount}
                      appConfig={appConfig}
                      availableBalance={availableBalance}
                      withdrawalStatus={withdrawalStatus}
                      isWalletConnected={!!tonConnectUI.account?.address}
                    />

                    <WithdrawDrawerActions
                      onWithdraw={handleWithdraw}
                      onCancel={handleClose}
                      isValidAmount={isValidAmount}
                      loading={loading}
                      statusLoading={statusLoading}
                      isWalletConnected={!!tonConnectUI.account?.address}
                      withdrawalStatus={withdrawalStatus}
                      withdrawAmount={withdrawAmount}
                      netAmount={netAmount}
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
