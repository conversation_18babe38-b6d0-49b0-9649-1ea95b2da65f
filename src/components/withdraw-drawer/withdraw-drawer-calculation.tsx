import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

interface WithdrawDrawerCalculationProps {
  withdrawAmount: string;
  withdrawFee: number;
  netAmount: number;
}

interface CalculationRowProps {
  label: string;
  amount: number;
  isNegative?: boolean;
  isTotal?: boolean;
}

function CalculationRow({
  label,
  amount,
  isNegative = false,
  isTotal = false,
}: CalculationRowProps) {
  const textColor = isTotal
    ? 'text-[#f5f5f5]'
    : isNegative
      ? 'text-[#ec3942]'
      : 'text-[#6ab2f2]';

  const borderClass = isTotal ? 'border-t border-[#3a4a5c]/30 pt-3' : '';
  const fontWeight = isTotal ? 'font-medium' : '';

  return (
    <div
      className={`flex justify-between items-center ${fontWeight} ${borderClass}`}
    >
      <span className={isTotal ? 'text-[#f5f5f5]' : 'text-[#708499]'}>
        {label}
      </span>
      <div className="flex items-center gap-1">
        <span className={`${textColor} font-semibold`}>
          {isNegative ? '-' : ''}
          {amount.toFixed(2)}
        </span>
        <TonLogo size={24} />
      </div>
    </div>
  );
}

export function WithdrawDrawerCalculation({
  withdrawAmount,
  withdrawFee,
  netAmount,
}: WithdrawDrawerCalculationProps) {
  const { formatMessage: t } = useIntl();

  if (!withdrawAmount) return null;

  const amount = parseFloat(withdrawAmount);
  if (isNaN(amount)) return null;

  return (
    <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30 mt-3">
      <div className="space-y-3">
        <CalculationRow
          label={t(withdrawDrawerMessages.withdrawAmount)}
          amount={amount}
        />
        <CalculationRow
          label={t(withdrawDrawerMessages.withdrawalFee)}
          amount={withdrawFee}
          isNegative={true}
        />
        <CalculationRow
          label={t(withdrawDrawerMessages.youWillReceive)}
          amount={netAmount}
          isTotal={true}
        />
      </div>
    </div>
  );
}
