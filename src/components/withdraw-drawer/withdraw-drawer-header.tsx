import { Caption } from '@telegram-apps/telegram-ui';
import { ArrowDown } from 'lucide-react';
import { useIntl } from 'react-intl';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

export function WithdrawDrawerHeader() {
  const { formatMessage: t } = useIntl();

  return (
    <div className="flex items-center gap-3 mb-6">
      <div className="w-10 h-10 bg-[#ec3942] rounded-full flex items-center justify-center">
        <ArrowDown className="w-5 h-5 text-white" />
      </div>
      <div>
        <h2 className="text-xl font-semibold text-[#f5f5f5]">
          {t(withdrawDrawerMessages.withdrawFunds)}
        </h2>
        <Caption level="2" weight="3" className="text-[#708499]">
          {t(withdrawDrawerMessages.withdrawTonToWallet)}
        </Caption>
      </div>
    </div>
  );
}
