interface AppConfig {
  withdrawal_fee: number;
  min_withdrawal_amount?: number;
}

interface UserBalance {
  sum: number;
  locked: number;
}

interface WithdrawalStatus {
  remainingLimit: number;
}

export function validateWithdrawAmount(
  amount: string,
  appConfig?: AppConfig | null,
  userBalance?: UserBalance,
  withdrawalStatus?: WithdrawalStatus | null,
): boolean {
  if (!amount || !appConfig || !userBalance || !withdrawalStatus) {
    return false;
  }

  const numAmount = parseFloat(amount);
  if (isNaN(numAmount) || numAmount <= 0) {
    return false;
  }

  if (numAmount < (appConfig.min_withdrawal_amount ?? 1)) {
    return false;
  }

  const availableBalance = userBalance.sum - userBalance.locked;
  if (numAmount > availableBalance) {
    return false;
  }

  if (numAmount > withdrawalStatus.remainingLimit) {
    return false;
  }

  return true;
}

export function calculateAvailableBalance(userBalance?: UserBalance): number {
  if (!userBalance) return 0;
  return userBalance.sum - userBalance.locked;
}

export function calculateWithdrawFee(
  withdrawAmount: string,
  appConfig?: AppConfig | null,
): number {
  if (!appConfig || !withdrawAmount) return 0;
  const amount = parseFloat(withdrawAmount);
  if (isNaN(amount)) return 0;
  return appConfig.withdrawal_fee;
}

export function calculateNetAmount(
  withdrawAmount: string,
  withdrawFee: number,
): number {
  if (!withdrawAmount) return 0;
  const amount = parseFloat(withdrawAmount);
  if (isNaN(amount)) return 0;
  return Math.max(0, amount - withdrawFee);
}
