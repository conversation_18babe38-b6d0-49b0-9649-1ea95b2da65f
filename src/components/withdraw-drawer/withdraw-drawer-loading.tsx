import { Caption } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

export function WithdrawDrawerLoading() {
  const { formatMessage: t } = useIntl();

  return (
    <div className="text-center py-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6ab2f2] mx-auto"></div>
      <Caption level="2" weight="3" className="text-[#708499] mt-2">
        {t(withdrawDrawerMessages.loadingConfiguration)}
      </Caption>
    </div>
  );
}
