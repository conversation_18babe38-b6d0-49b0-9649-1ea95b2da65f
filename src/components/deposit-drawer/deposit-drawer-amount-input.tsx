import { Caption } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { depositDrawerAmountInputMessages } from './intl/deposit-drawer-amount-input.messages';

interface DepositDrawerAmountInputProps {
  value: string;
  onChange: (value: string) => void;
  minAmount: number;
  isValid: boolean;
  hasValue: boolean;
}

export function DepositDrawerAmountInput({
  value,
  onChange,
  minAmount,
  isValid,
  hasValue,
}: DepositDrawerAmountInputProps) {
  const { formatMessage: t } = useIntl();

  return (
    <div>
      <Label
        htmlFor="deposit-amount"
        className="text-sm font-medium text-foreground"
      >
        {t(depositDrawerAmountInputMessages.depositAmountTon)}
      </Label>
      <Input
        id="deposit-amount"
        type="number"
        placeholder={t(depositDrawerAmountInputMessages.minTonPlaceholder, {
          amount: minAmount,
        })}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="mt-2 bg-card/50 border-border/50 text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/20"
        min={minAmount}
        step="0.1"
      />
      {hasValue && !isValid && (
        <Caption level="2" weight="3" className="text-destructive mt-1">
          {t(depositDrawerAmountInputMessages.amountMustBeAtLeast, {
            amount: minAmount,
          })}
        </Caption>
      )}
    </div>
  );
}
