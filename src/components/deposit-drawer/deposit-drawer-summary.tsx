import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';

import { depositDrawerSummaryMessages } from './intl/deposit-drawer-summary.messages';

interface DepositDrawerSummaryProps {
  depositAmount: string;
  depositFee: number;
}

export function DepositDrawerSummary({
  depositAmount,
  depositFee,
}: DepositDrawerSummaryProps) {
  const { formatMessage: t } = useIntl();
  const totalAmount = parseFloat(depositAmount) + depositFee;

  return (
    <div className="bg-card/50 rounded-2xl p-4 border border-border/30">
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-muted-foreground">
            {t(depositDrawerSummaryMessages.depositAmount)}
          </span>
          <div className="flex items-center gap-1">
            <span className="text-primary font-semibold">{depositAmount}</span>
            <TonLogo size={24} />
          </div>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-muted-foreground">
            {t(depositDrawerSummaryMessages.depositFee)}
          </span>
          <div className="flex items-center gap-1">
            <span className="text-primary font-semibold">{depositFee}</span>
            <TonLogo size={24} />
          </div>
        </div>
        <div className="flex justify-between items-center font-medium border-t border-border/30 pt-3">
          <span className="text-foreground">
            {t(depositDrawerSummaryMessages.totalToPay)}
          </span>
          <div className="flex items-center gap-1">
            <span className="text-primary font-semibold">
              {totalAmount.toFixed(1)}
            </span>
            <TonLogo size={24} />
          </div>
        </div>
      </div>
    </div>
  );
}
