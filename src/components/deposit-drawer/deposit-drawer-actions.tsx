import { Caption } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';

import { depositDrawerActionsMessages } from './intl/deposit-drawer-actions.messages';

interface DepositDrawerActionsProps {
  onDeposit: () => void;
  onCancel: () => void;
  isValidAmount: boolean;
  loading: boolean;
  isWalletConnected: boolean;
  depositAmount: string;
  totalAmount?: number;
}

export function DepositDrawerActions({
  onDeposit,
  onCancel,
  isValidAmount,
  loading,
  isWalletConnected,
  depositAmount,
  totalAmount,
}: DepositDrawerActionsProps) {
  const { formatMessage: t } = useIntl();
  const isDepositDisabled = !isValidAmount || loading || !isWalletConnected;

  return (
    <div className="space-y-3 pt-4">
      <Button
        onClick={onDeposit}
        disabled={isDepositDisabled}
        className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground border-0 rounded-2xl"
      >
        {loading ? (
          t(depositDrawerActionsMessages.processing)
        ) : (
          <>
            {t(depositDrawerActionsMessages.deposit)}{' '}
            {depositAmount && isValidAmount && totalAmount ? (
              <>
                &#40;
                {totalAmount.toFixed(1)} <TonLogo className="-m-2" size={24} />
                <span className="-ml-1 translate-x-[1px]">&#41;</span>
              </>
            ) : (
              ''
            )}
          </>
        )}
      </Button>

      <Button
        variant="outline"
        onClick={onCancel}
        className="w-full h-12 bg-transparent border-border/50 text-muted-foreground hover:bg-card/50 hover:text-foreground rounded-2xl"
        disabled={loading}
      >
        {t(depositDrawerActionsMessages.cancel)}
      </Button>

      {!isWalletConnected && (
        <div className="text-center mt-4">
          <Caption level="2" weight="3" className="text-destructive">
            {t(depositDrawerActionsMessages.pleaseConnectWallet)}
          </Caption>
        </div>
      )}
    </div>
  );
}
