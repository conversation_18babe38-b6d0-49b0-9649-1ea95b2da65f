import { defineMessages } from 'react-intl';

export const withdrawDrawerMessages = defineMessages({
  withdrawFunds: {
    id: 'withdrawDrawer.withdrawFunds',
    defaultMessage: 'Withdraw Funds',
  },
  withdrawTonToWallet: {
    id: 'withdrawDrawer.withdrawTonToWallet',
    defaultMessage: 'Withdraw TON to your connected wallet',
  },
  loadingConfiguration: {
    id: 'withdrawDrawer.loadingConfiguration',
    defaultMessage: 'Loading configuration...',
  },
  withdrawalInformation: {
    id: 'withdrawDrawer.withdrawalInformation',
    defaultMessage: 'Withdrawal Information',
  },
  minimumWithdrawal: {
    id: 'withdrawDrawer.minimumWithdrawal',
    defaultMessage: 'Minimum withdrawal:',
  },
  withdrawalFee: {
    id: 'withdrawDrawer.withdrawalFee',
    defaultMessage: 'Withdrawal fee:',
  },
  availableBalance: {
    id: 'withdrawDrawer.availableBalance',
    defaultMessage: 'Available balance:',
  },
  withdrawAmountTon: {
    id: 'withdrawDrawer.withdrawAmountTon',
    defaultMessage: 'Withdraw Amount (TON)',
  },
  insufficientBalance: {
    id: 'withdrawDrawer.insufficientBalance',
    defaultMessage: 'Insufficient available balance',
  },
  withdrawAmount: {
    id: 'withdrawDrawer.withdrawAmount',
    defaultMessage: 'Withdraw amount:',
  },
  netAmount: {
    id: 'withdrawDrawer.netAmount',
    defaultMessage: 'Net amount:',
  },
  processing: {
    id: 'withdrawDrawer.processing',
    defaultMessage: 'Processing...',
  },
  withdraw: {
    id: 'withdrawDrawer.withdraw',
    defaultMessage: 'Withdraw',
  },
  cancel: {
    id: 'withdrawDrawer.cancel',
    defaultMessage: 'Cancel',
  },
  pleaseConnectWallet: {
    id: 'withdrawDrawer.pleaseConnectWallet',
    defaultMessage: 'Please connect your wallet to make a withdrawal',
  },
  withdrawalSuccessful: {
    id: 'withdrawDrawer.withdrawalSuccessful',
    defaultMessage: 'Withdrawal successful! Transaction: {hash}',
  },
  withdrawalFailed: {
    id: 'withdrawDrawer.withdrawalFailed',
    defaultMessage: 'Withdrawal failed: {message}',
  },
  unexpectedError: {
    id: 'withdrawDrawer.unexpectedError',
    defaultMessage: 'An unexpected error occurred',
  },
  invalidWithdrawalAmount: {
    id: 'withdrawDrawer.invalidWithdrawalAmount',
    defaultMessage: 'Invalid withdrawal amount',
  },
  pleaseConnectWalletFirst: {
    id: 'withdrawDrawer.pleaseConnectWalletFirst',
    defaultMessage: 'Please connect your wallet first',
  },
  noWalletAddressFound: {
    id: 'withdrawDrawer.noWalletAddressFound',
    defaultMessage: 'No wallet address found in your profile',
  },
  enterAmountToWithdraw: {
    id: 'withdrawDrawer.enterAmountToWithdraw',
    defaultMessage: 'Enter amount to withdraw',
  },
  youWillReceive: {
    id: 'withdrawDrawer.youWillReceive',
    defaultMessage: 'You will receive:',
  },
  minimumWithdrawalAmount: {
    id: 'withdrawDrawer.minimumWithdrawalAmount',
    defaultMessage: 'Minimum withdrawal amount is {minAmount} TON',
  },
  withdrawalLimit24h: {
    id: 'withdrawDrawer.withdrawalLimit24h',
    defaultMessage: '24-hour withdrawal limit:',
  },
  remainingLimit: {
    id: 'withdrawDrawer.remainingLimit',
    defaultMessage: 'Remaining limit:',
  },
  limitResetsAt: {
    id: 'withdrawDrawer.limitResetsAt',
    defaultMessage: 'Limit resets at:',
  },
  insufficientAvailableBalance: {
    id: 'withdrawDrawer.insufficientAvailableBalance',
    defaultMessage: 'Insufficient available balance',
  },
  invalidAmount: {
    id: 'withdrawDrawer.invalidAmount',
    defaultMessage: 'Invalid amount',
  },
  max: {
    id: 'withdrawDrawer.max',
    defaultMessage: 'Max',
  },
  exceeds24HourLimit: {
    id: 'withdrawDrawer.exceeds24HourLimit',
    defaultMessage: 'Exceeds 24-hour limit. Remaining: {remainingAmount} TON',
  },
});
