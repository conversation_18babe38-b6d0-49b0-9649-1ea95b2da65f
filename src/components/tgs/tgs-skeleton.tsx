'use client';

import { cn } from '@/lib/utils';

interface TgsSkeletonProps {
  className?: string;
  style?: React.CSSProperties;
}

export function TgsSkeleton({ className, style }: TgsSkeletonProps) {
  return (
    <div
      className={cn(
        'flex w-full h-full relative items-center justify-center bg-background rounded-lg animate-pulse',
        className,
      )}
      style={style}
    >
      <div className="absolute top-0 left-0 w-full h-full bg-border animate-pulse rounded-lg" />
    </div>
  );
}
