export const APP_NAME = "PREM";
export const CDN_URL = "https://cdn.changes.tg/gifts";

export const BOT_TOKEN = process.env.BOT_TOKEN;

export const PREM_RELAYER_USERNAME = `@${APP_NAME.toLowerCase()}relayer`;

export const PREM_CHANNEL = `@${APP_NAME.toLowerCase()}_channel`;
export const PREM_SUPPORT_OFFICIAL = `@${APP_NAME.toLowerCase()}_support_official`;

export const WEB_APP_URL = process.env.WEB_APP_URL as string;

export const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID;

export const PORT = process.env.PORT ?? 8080;
export const NODE_ENV = process.env.NODE_ENV ?? "development";
export const WEBHOOK_URL = process.env.WEBHOOK_URL;
export const BOT_NAME = process.env.BOT_NAME;

export enum BotCommands {
  START = "start",
  HELP = "help",
  HEALTH = "health",
}

export enum ButtonMessageIds {
  MY_GIFTS = "buttons.myGifts",
  DEPOSIT_GIFT = "buttons.depositGift",
  CONTACT_SUPPORT = "buttons.contactSupport",
}

export enum CallbackActions {
  ORDER_HELP = "order_help",
  CONTACT_SUPPORT = "contact_support",
  OPEN_MARKETPLACE = "open_marketplace",
  BACK_TO_MENU = "back_to_menu",
}

export enum CallbackPatterns {
  ORDER_SELECTION = "^order_(.+)$",
  ORDER_COMPLETION = "^complete_(.+)$",
  RECEIVE_GIFT = "^receive_gift_(.+)$",
}

export enum LogOperations {
  // Bot flows
  BUYER_GET_GIFT = "buyer_get_gift",
  ACTIVATE_ORDER_WITH_GIFT = "activate_order_with_gift",
  GET_CANCELLED_GIFT = "get_cancelled_gift",
  SEND_GIFT_TO_PAID_ORDER = "send_gift_to_paid_order",
  NOTIFY_SELLER_ORDER_PAID = "notify_seller_order_paid",
  NOTIFY_BUYER_GIFT_SENT = "notify_buyer_gift_sent",

  // Middleware operations
  BUSINESS_CONNECTION_MIDDLEWARE = "business_connection_middleware",
  BUSINESS_CONNECTION_MIDDLEWARE_START = "business_connection_middleware_start",
  BUSINESS_CONNECTION_MIDDLEWARE_END = "business_connection_middleware_end",

  // Server operations
  HTTP_SERVER = "http_server",
  HTTP_REQUEST = "http_request",
  HTTP_SERVER_START = "http_server_start",
  HTTP_SERVER_STOP = "http_server_stop",
  WEBHOOK_PROCESSING = "webhook_processing",
  HEALTHCHECK = "healthcheck",
  HEALTHCHECK_STATUS = "healthcheck_status",

  // Core logging operations
  BOT_OPERATION = "bot_operation",
  BOT_ERROR = "bot_error",
  BOT_STARTUP = "bot_startup",
  BOT_CONFIGURATION = "bot_configuration",
  BOT_SHUTDOWN = "bot_shutdown",
  WEBHOOK_OPERATION = "webhook_operation",
  HEALTH_CHECK = "health_check",

  // Session operations
  SET_USER_SESSION = "set_user_session",
  GET_USER_SESSION = "get_user_session",
  CLEAR_USER_SESSION = "clear_user_session",
  UPDATE_USER_SESSION = "update_user_session",
  CLEAR_SESSION_PROPERTY = "clear_session_property",

  // Gift operations
  GIFT_VALIDATION = "gift_validation",
  GIFT_TRANSFER = "gift_transfer",
  GET_GIFT_BY_ID = "get_gift_by_id",
  MOCK_GIFT_DEPOSIT = "mock_gift_deposit",
  GIFT_WITHDRAWAL = "gift_withdrawal",
  CREATE_GIFT = "create_gift",
  DIRECT_GIFT_DEPOSIT = "direct_gift_deposit",
  UPDATE_GIFT_OWNERSHIP = "update_gift_ownership",

  // Order operations
  ORDER_PROCESSING = "order_processing",
  CREATE_SELL_ORDER_FROM_GIFT_START = "create_sell_order_from_gift_start",
  LINK_GIFT_TO_ORDER_START = "link_gift_to_order_start",

  // Button handlers
  GET_MY_GIFTS_BUTTON = "get_my_gifts_button",
  CLEAR_SESSION_ON_DEPOSIT = "clear_session_on_deposit",
  HEALTH_COMMAND = "health_command",
  DEPOSIT_GIFT_CLEAR_SESSION = "deposit_gift_clear_session",

  // Simulation operations
  SIMULATION_OPERATION = "simulation_operation",
  SIMULATION_DEPOSIT_GIFT = "simulation_deposit_gift",
  SIMULATION_DEPOSIT_GIFT_ERROR = "simulation_deposit_gift_error",
  SIMULATION_GIFT_WITHDRAWAL = "simulation_gift_withdrawal",
  SIMULATION_GIFT_WITHDRAWAL_ERROR = "simulation_gift_withdrawal_error",
  SIMULATION_CLEAR_SESSION = "simulation_clear_session",

  // Transaction operations
  TRANSACTION_PROCESSING = "transaction_processing",
  BALANCE_OPERATION = "balance_operation",
  FEE_PROCESSING = "fee_processing",

  // Monitoring
  MONITORING = "monitoring",
}
