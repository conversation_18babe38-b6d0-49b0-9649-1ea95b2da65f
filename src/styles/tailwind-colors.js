// Custom color configuration for Tailwind CSS
// This file extends Tailwind with our theme colors as CSS variables

const colors = {
  // Base colors
  white: 'var(--color-white)',
  black: 'var(--color-black)',
  transparent: 'var(--color-transparent)',

  // Gray scale
  gray: {
    50: 'var(--color-gray-50)',
    100: 'var(--color-gray-100)',
    200: 'var(--color-gray-200)',
    300: 'var(--color-gray-300)',
    400: 'var(--color-gray-400)',
    500: 'var(--color-gray-500)',
    600: 'var(--color-gray-600)',
    700: 'var(--color-gray-700)',
    800: 'var(--color-gray-800)',
    900: 'var(--color-gray-900)',
  },

  // Brand colors
  ton: {
    main: 'var(--color-ton-main)',
    gray: 'var(--color-ton-gray)',
    black: 'var(--color-ton-black)',
  },

  telegram: {
    blue: 'var(--color-telegram-blue)',
    'light-blue': 'var(--color-telegram-light-blue)',
    'dark-bg': 'var(--color-telegram-dark-bg)',
    'secondary-bg': 'var(--color-telegram-secondary-bg)',
    text: 'var(--color-telegram-text)',
    hint: 'var(--color-telegram-hint)',
  },

  // Status colors
  success: 'var(--color-success)',
  warning: 'var(--color-warning)',
  error: 'var(--color-error)',
  info: 'var(--color-info)',

  // Semantic colors (these map to CSS variables that change with theme)
  background: 'var(--color-background)',
  foreground: 'var(--color-foreground)',
  primary: {
    DEFAULT: 'var(--color-primary)',
    foreground: 'var(--color-primary-foreground)',
  },
  secondary: {
    DEFAULT: 'var(--color-secondary)',
    foreground: 'var(--color-secondary-foreground)',
  },
  accent: {
    DEFAULT: 'var(--color-accent)',
    foreground: 'var(--color-accent-foreground)',
  },
  destructive: {
    DEFAULT: 'var(--color-destructive)',
    foreground: 'var(--color-destructive-foreground)',
  },
  muted: {
    DEFAULT: 'var(--color-muted)',
    foreground: 'var(--color-muted-foreground)',
  },
  card: {
    DEFAULT: 'var(--color-card)',
    foreground: 'var(--color-card-foreground)',
  },
  popover: {
    DEFAULT: 'var(--color-popover)',
    foreground: 'var(--color-popover-foreground)',
  },
  border: 'var(--color-border)',
  input: 'var(--color-input)',
  ring: 'var(--color-ring)',

  // Chart colors
  chart: {
    1: 'var(--color-chart-1)',
    2: 'var(--color-chart-2)',
    3: 'var(--color-chart-3)',
    4: 'var(--color-chart-4)',
    5: 'var(--color-chart-5)',
  },

  // Status badge colors
  status: {
    yellow: {
      DEFAULT: 'var(--color-status-yellow)',
      bg: 'var(--color-status-yellow-bg)',
      border: 'var(--color-status-yellow-border)',
    },
    blue: {
      DEFAULT: 'var(--color-status-blue)',
      bg: 'var(--color-status-blue-bg)',
      border: 'var(--color-status-blue-border)',
    },
    green: {
      DEFAULT: 'var(--color-status-green)',
      bg: 'var(--color-status-green-bg)',
      border: 'var(--color-status-green-border)',
    },
    purple: {
      DEFAULT: 'var(--color-status-purple)',
      bg: 'var(--color-status-purple-bg)',
      border: 'var(--color-status-purple-border)',
    },
    pink: {
      DEFAULT: 'var(--color-status-pink)',
      bg: 'var(--color-status-pink-bg)',
    },
    orange: {
      DEFAULT: 'var(--color-status-orange)',
      bg: 'var(--color-status-orange-bg)',
      border: 'var(--color-status-orange-border)',
    },
  },

  // Transaction colors
  transaction: {
    positive: 'var(--color-transaction-positive)',
    negative: 'var(--color-transaction-negative)',
  },

  // Telegram theme colors (for direct access)
  tg: {
    bg: 'var(--color-tg-bg)',
    text: 'var(--color-tg-text)',
    hint: 'var(--color-tg-hint)',
    link: 'var(--color-tg-link)',
    button: 'var(--color-tg-button)',
    'button-text': 'var(--color-tg-button-text)',
    'secondary-bg': 'var(--color-tg-secondary-bg)',
    'header-bg': 'var(--color-tg-header-bg)',
    'bottom-bar-bg': 'var(--color-tg-bottom-bar-bg)',
    'accent-text': 'var(--color-tg-accent-text)',
    'section-bg': 'var(--color-tg-section-bg)',
    'section-header-text': 'var(--color-tg-section-header-text)',
    'section-separator': 'var(--color-tg-section-separator)',
    'subtitle-text': 'var(--color-tg-subtitle-text)',
    'destructive-text': 'var(--color-tg-destructive-text)',
  },
};

module.exports = { colors };
