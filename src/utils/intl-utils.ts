import { AppLocale } from '@/core.constants';

export const LANGUAGE_LABELS = {
  [AppLocale.en]: 'English',
  [AppLocale.ru]: 'Русский',
  [AppLocale.uk]: 'Українська',
};

export const getUserCountry = (): string => {
  if (typeof window === 'undefined') return 'unknown';

  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const locale = navigator.language || navigator.languages?.[0] || '';

    if (
      timezone.includes('Kiev') ||
      timezone.includes('Kyiv') ||
      locale.toLowerCase().includes('ua')
    ) {
      return 'UA';
    }

    if (
      timezone.includes('Moscow') ||
      timezone.includes('Europe/Moscow') ||
      locale.toLowerCase().startsWith('ru')
    ) {
      return 'RU';
    }

    if (timezone.includes('Minsk') || locale.toLowerCase().includes('by')) {
      return 'BY';
    }

    return 'OTHER';
  } catch {
    return 'OTHER';
  }
};

export const getAvailableLanguages = (country: string): AppLocale[] => {
  switch (country) {
    case 'UA':
      return [AppLocale.en, AppLocale.uk];
    case 'RU':
    case 'BY':
      return [AppLocale.en, AppLocale.ru];
    default:
      return [AppLocale.en];
  }
};
